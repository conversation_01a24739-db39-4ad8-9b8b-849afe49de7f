import openpyxl
from openpyxl import load_workbook
from openpyxl.styles import Alignment, Font, PatternFill
import os
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from PIL import Image, ImageTk
import ezdxf
import subprocess
from collections import Counter
import datetime
import pandas as pd
import xlrd
import re
import sys
def resource_path(relative_path):
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

# 然后在加载图片的地方使用这个函数
logo_img = Image.open(resource_path("logo.png"))
machine_img = Image.open(resource_path("hydraulic_press.png"))


class ExcelToolApp:
    def create_header(self):
        header_content = ttk.Frame(self.header_frame)
        header_content.pack(fill='x', padx=20, pady=10)

        try:
            logo_img = Image.open("logo.png")
            original_width, original_height = logo_img.size
            new_height = 50
            new_width = int(original_width * (new_height / original_height))
            logo_img = logo_img.resize((new_width, new_height), Image.LANCZOS)
            self.logo_photo = ImageTk.PhotoImage(logo_img)
            logo_label = ttk.Label(header_content, image=self.logo_photo, background=self.primary_color)
            logo_label.pack(side='left')
        except Exception as e:
            print(f"Logo加载错误: {str(e)}")
            logo_label = ttk.Label(
                header_content,
                text="通用技术天锻公司",
                style='Header.TLabel'
            )
            logo_label.pack(side='left', padx=20)

        title_frame = ttk.Frame(header_content, style='Header.TFrame')
        title_frame.pack(side='right', padx=20)

        title_label = ttk.Label(
            title_frame,
            text="重型装备数据处理系统",
            style='Header.TLabel',
            font=('Microsoft YaHei UI', 24, 'bold')
        )
        title_label.pack()

        version_label = ttk.Label(
            title_frame,
            text="Version 1.0",
            style='Header.TLabel',
            font=('Microsoft YaHei UI', 10)
        )
        version_label.pack()
    def create_side_panel(self):
        side_frame = ttk.Frame(self.content_area, style='Content.TFrame')
        side_frame.pack(side='left', fill='y', padx=10)

        try:
            machine_img = Image.open("hydraulic_press.png")
            original_width, original_height = machine_img.size
            new_width = 300
            new_height = int(original_height * (new_width / original_width))
            machine_img = machine_img.resize((new_width, new_height), Image.LANCZOS)
            self.machine_photo = ImageTk.PhotoImage(machine_img)
            machine_label = ttk.Label(side_frame, image=self.machine_photo, background='white')
            machine_label.pack(pady=10)

            # 公司信息框架
            info_frame = ttk.Frame(side_frame, style='Content.TFrame')
            info_frame.pack(fill='x', pady=10)
            company_info = ttk.Label(
                info_frame,
                text="天锻重工\n专注于重型装备制造\n致力于技术创新",
                font=('Microsoft YaHei UI', 12),
                foreground=self.text_color,
                background='white',
                justify='center'
            )
            company_info.pack(expand=True)

            # 添加帮助文本区域
            help_text = """使用说明

1、选择文件
数据储存：选择"密封明细统计"作为数据存储文件
油箱明细表/标准件表/BOM表：可选择多个Excel文件
DWG文件：需要时同时选择DWG文件夹和DXF输出文件夹
      注：可同时处理或分别处理一种文件

2、点击"开始处理"，等待处理完成

3、结果查看
Sheet1的A-D列：提取的零件信息
Sheet1的F-G列：密封规格统计结果
红色背景：表示未找到对应密封规格

4、注意事项
使用前，请先安装附件中中"ODAFileConverter_QT6_vc16_amd64dll_25.8.msi"软件到"D:\software\ODA"
Excel：.xlsx、.xlsm、.xls
图纸：.dwg
处理前会自动清空"密封明细统计"工作簿数据区域，请注意备份重要数据。

5、如有问题或改进建议请联系重型装备设计所-胡猛超。"""

            # 创建文本框
            self.help_text = tk.Text(side_frame, 
                                   wrap=tk.WORD, 
                                   width=35, 
                                   height=15,
                                   font=('Microsoft YaHei UI', 10),
                                   background='white',
                                   foreground=self.text_color)
            self.help_text.pack(pady=10, fill='both', expand=True)
            self.help_text.insert('1.0', help_text)
            self.help_text.configure(state='disabled')  # 使文本不可编辑

            # 设置自动滚动
            self.scroll_paused = False
            self.current_scroll_pos = 0.0
            self.help_text.bind('<Enter>', self.pause_scroll)
            self.help_text.bind('<Leave>', self.resume_scroll)
            self.after_id = None
            self.scroll_text()

        except Exception as e:
            print(f"图片加载错误: {str(e)}")
            info_frame = ttk.Frame(side_frame, style='Content.TFrame')
            info_frame.pack(pady=10)
            info_frame.configure(width=300, height=400)
            company_info = ttk.Label(
                info_frame,
                text="天锻重工\n专注于重型装备制造\n致力于技术创新",
                font=('Microsoft YaHei UI', 12),
                foreground=self.text_color,
                justify='center'
            )
            company_info.pack(expand=True)

    def create_main_content(self):
        self.content_frame = ttk.Frame(self.content_area, style='Content.TFrame')
        self.content_frame.pack(side='right', fill='both', expand=True, padx=10)

        self.create_file_selection_frame()
        self.create_keyword_frame()
        self.create_process_button()
        self.create_bom_button()

    def create_file_selection_frame(self):
        file_frame = ttk.LabelFrame(
            self.content_frame,
            text="文件选择",
            padding="15",
            style='Custom.TLabelframe'
        )
        file_frame.pack(fill='x', pady=10)

        # 工作簿A选择
        file_a_frame = ttk.Frame(file_frame)
        file_a_frame.pack(fill='x', pady=8)
        self.label_a = ttk.Label(file_a_frame, text="数据储存:", font=('Microsoft YaHei UI', 10))
        self.label_a.pack(side='left', padx=5)
        self.button_a = ttk.Button(
            file_a_frame,
            text="选择文件",
            command=self.select_file_a,
            style='Custom.TButton'
        )
        self.button_a.pack(side='right', padx=5)

        # 工作簿B选择
        file_b_frame = ttk.Frame(file_frame)
        file_b_frame.pack(fill='x', pady=8)
        self.label_b = ttk.Label(file_b_frame, text="油箱明细表:", font=('Microsoft YaHei UI', 10))
        self.label_b.pack(side='left', padx=5)
        self.button_b = ttk.Button(
            file_b_frame,
            text="选择文件",
            command=self.select_files_b,
            style='Custom.TButton'
        )
        self.button_b.pack(side='right', padx=5)

        # 工作簿C选择
        file_c_frame = ttk.Frame(file_frame)
        file_c_frame.pack(fill='x', pady=8)
        self.label_c = ttk.Label(file_c_frame, text="标准件表:", font=('Microsoft YaHei UI', 10))
        self.label_c.pack(side='left', padx=5)
        self.button_c = ttk.Button(
            file_c_frame,
            text="选择文件",
            command=self.select_files_c,
            style='Custom.TButton'
        )
        self.button_c.pack(side='right', padx=5)

        # BOM表选择（原工作簿D）
        file_d_frame = ttk.Frame(file_frame)
        file_d_frame.pack(fill='x', pady=8)
        self.label_d = ttk.Label(file_d_frame, text="BOM表:", font=('Microsoft YaHei UI', 10))
        self.label_d.pack(side='left', padx=5)
        self.button_d = ttk.Button(
            file_d_frame,
            text="选择文件",
            command=self.select_files_d,
            style='Custom.TButton'
        )
        self.button_d.pack(side='right', padx=5)

        # DWG文件夹选择
        dwg_frame = ttk.Frame(file_frame)
        dwg_frame.pack(fill='x', pady=8)
        self.label_dwg = ttk.Label(dwg_frame, text="DWG文件夹:", font=('Microsoft YaHei UI', 10))
        self.label_dwg.pack(side='left', padx=5)
        self.button_dwg = ttk.Button(
            dwg_frame,
            text="选择文件夹",
            command=self.select_dwg_folder,
            style='Custom.TButton'
        )
        self.button_dwg.pack(side='right', padx=5)

        # DXF输出文件夹选择
        dxf_frame = ttk.Frame(file_frame)
        dxf_frame.pack(fill='x', pady=8)
        self.label_dxf = ttk.Label(dxf_frame, text="DXF输出文件夹:", font=('Microsoft YaHei UI', 10))
        self.label_dxf.pack(side='left', padx=5)
        self.button_dxf = ttk.Button(
            dxf_frame,
            text="选择文件夹",
            command=self.select_dxf_folder,
            style='Custom.TButton'
        )
        self.button_dxf.pack(side='right', padx=5)
    def create_keyword_frame(self):
        keyword_frame = ttk.LabelFrame(
            self.content_frame,
            text="关键词设置",
            padding="15",
            style='Custom.TLabelframe'
        )
        keyword_frame.pack(fill='x', pady=10)

        # 工作簿B关键词设置
        wb_b_frame = ttk.Frame(keyword_frame)
        wb_b_frame.pack(fill='x', pady=8)

        ttk.Label(wb_b_frame, text="油箱明细表/标准件表关键词来源：", font=('Microsoft YaHei UI', 10)).pack(side='left', padx=5)
        ttk.Radiobutton(wb_b_frame, text="从Sheet2的H列读取", variable=self.keyword_source_b,
                       value="sheet", command=self.toggle_keyword_input_b).pack(side='left', padx=5)
        ttk.Radiobutton(wb_b_frame, text="手动输入", variable=self.keyword_source_b,
                       value="manual", command=self.toggle_keyword_input_b).pack(side='left', padx=5)

        self.entry_keywords_b_frame = ttk.Frame(keyword_frame)
        self.entry_keywords_b_frame.pack(fill='x', pady=8)
        ttk.Label(self.entry_keywords_b_frame, text="输入油箱明细表/标准件表关键词（多个关键词用逗号分隔）:",
                 font=('Microsoft YaHei UI', 10)).pack(side='left', padx=5)
        self.entry_keywords_b = ttk.Entry(self.entry_keywords_b_frame, font=('Microsoft YaHei UI', 10))
        self.entry_keywords_b.pack(side='left', padx=5, expand=True, fill='x')

        # DXF关键词设置
        dxf_frame = ttk.Frame(keyword_frame)
        dxf_frame.pack(fill='x', pady=8)

        ttk.Label(dxf_frame, text="DXF文件关键词来源：", font=('Microsoft YaHei UI', 10)).pack(side='left', padx=5)
        ttk.Radiobutton(dxf_frame, text="从Sheet2的J列读取", variable=self.keyword_source_dxf,
                       value="sheet", command=self.toggle_keyword_input_dxf).pack(side='left', padx=5)
        ttk.Radiobutton(dxf_frame, text="手动输入", variable=self.keyword_source_dxf,
                       value="manual", command=self.toggle_keyword_input_dxf).pack(side='left', padx=5)

        self.entry_keywords_dxf_frame = ttk.Frame(keyword_frame)
        self.entry_keywords_dxf_frame.pack(fill='x', pady=8)
        ttk.Label(self.entry_keywords_dxf_frame, text="输入DXF文件关键词（多个关键词用逗号分隔）:",
                 font=('Microsoft YaHei UI', 10)).pack(side='left', padx=5)
        self.entry_keywords_dxf = ttk.Entry(self.entry_keywords_dxf_frame, font=('Microsoft YaHei UI', 10))
        self.entry_keywords_dxf.pack(side='left', padx=5, expand=True, fill='x')

    def create_process_button(self):
        self.button_frame = ttk.Frame(self.content_frame, style='Content.TFrame')
        self.button_frame.pack(pady=20, fill='x')

        self.style.configure('Process.TButton',
                           font=('Microsoft YaHei UI', 14, 'bold'),
                           padding=20)

        button_container = ttk.Frame(self.button_frame, style='Content.TFrame')
        button_container.pack(expand=True)

        self.process_button = ttk.Button(
            button_container,
            text="开始处理",
            command=self.process_files,
            style='Process.TButton'
        )
        self.process_button.pack(pady=15)

    def create_bom_button(self):
        # BOM表制作工具按钮
        bom_button = ttk.Button(
            self.content_frame,
            text="BOM表制作工具",
            command=self.open_bom_tool,
            style='Large.TButton',
            padding="20"
        )
        bom_button.pack(pady=20)

    def create_status_bar(self):
        self.status_frame = ttk.Frame(self.main_bg, style='Main.TFrame')
        self.status_frame.pack(side='bottom', fill='x', pady=5)

        self.time_label = ttk.Label(
            self.status_frame,
            text="",
            font=('Microsoft YaHei UI', 10),
            foreground=self.text_color
        )
        self.time_label.pack(side='left', padx=20)

        self.copyright_label = ttk.Label(
            self.status_frame,
            text="版权所有 © 2024 天锻重工",
            font=('Microsoft YaHei UI', 10),
            foreground=self.text_color
        )
        self.copyright_label.pack(side='right', padx=20)
    def __init__(self, master):
        self.master = master
        master.title("重型装备数据处理系统 - 天锻公司")

        # 增加窗口大小
        window_width = 1400
        window_height = 900
        screen_width = master.winfo_screenwidth()
        screen_height = master.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        master.geometry(f"{window_width}x{window_height}+{x}+{y}")

        self.style = ttk.Style()
        self.style.theme_use('clam')

        # 定义主题颜色
        self.primary_color = '#1E3F66'  # 深蓝色
        self.secondary_color = '#2E5C88'  # 中蓝色
        self.accent_color = '#4A90E2'  # 亮蓝色
        self.bg_color = '#F5F6FA'  # 浅灰色背景
        self.text_color = '#2C3E50'  # 文字颜色

        # 配置样式
        self.style.configure('Main.TFrame', background=self.bg_color)
        self.style.configure('Header.TFrame', background=self.primary_color)
        self.style.configure('Content.TFrame', background='white')

        self.style.configure('Custom.TButton',
                             font=('Microsoft YaHei UI', 10),
                             padding=5)

        self.style.configure('Header.TLabel',
                             font=('Microsoft YaHei UI', 12, 'bold'),
                             foreground='white',
                             background=self.primary_color)

        self.style.configure('Custom.TLabelframe',
                             background='white',
                             font=('Microsoft YaHei UI', 10))

        self.style.configure('Custom.TLabelframe.Label',
                             font=('Microsoft YaHei UI', 10, 'bold'),
                             foreground=self.text_color)

        # 创建主框架
        self.main_bg = ttk.Frame(master, style='Main.TFrame')
        self.main_bg.pack(fill='both', expand=True)

        # 创建顶部栏
        self.header_frame = ttk.Frame(self.main_bg, style='Header.TFrame')
        self.header_frame.pack(fill='x', pady=0)

        # 创建内容区域
        self.content_area = ttk.Frame(self.main_bg, style='Main.TFrame')
        self.content_area.pack(fill='both', expand=True, padx=20, pady=10)

        # 初始化变量
        self.file_a = None
        self.files_b = []
        self.files_c = []
        self.files_d = []
        self.dwg_folder = None
        self.dxf_folder = None
        self.keyword_source_b = tk.StringVar(value="sheet")
        self.keyword_source_dxf = tk.StringVar(value="sheet")

        # 创建界面元素
        self.create_header()
        self.create_side_panel()
        self.create_main_content()
        self.create_status_bar()

        # Add a protocol handler for closing the window
        self.master.protocol("WM_DELETE_WINDOW", self.on_closing)

    def update_time(self):
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=f"当前时间：{current_time}")
        self.master.after(1000, self.update_time)

    def toggle_keyword_input_b(self):
        if self.keyword_source_b.get() == "manual":
            self.entry_keywords_b.config(state='normal')
        else:
            self.entry_keywords_b.delete(0, tk.END)
            self.entry_keywords_b.config(state='disabled')
        # 初始化时调用一次toggle函数
        self.entry_keywords_b.config(state='disabled')  # 默认禁用输入框

    def toggle_keyword_input_dxf(self):
        if self.keyword_source_dxf.get() == "manual":
            self.entry_keywords_dxf.config(state='normal')
        else:
            self.entry_keywords_dxf.delete(0, tk.END)
            self.entry_keywords_dxf.config(state='disabled')
        # 初始化时调用一次toggle函数
        self.entry_keywords_dxf.config(state='disabled')  # 默认禁用输入框
    def select_file_a(self):
        self.file_a = filedialog.askopenfilename(
            title="选择数据储存",
            filetypes=[("Excel files", "*.xlsx;*.xlsm;*.xls")]
        )
        if self.file_a:
            self.label_a.config(text=f"已选择: {os.path.basename(self.file_a)}")

    def select_files_b(self):
        self.files_b = filedialog.askopenfilenames(
            title="选择多个油箱明细表",
            filetypes=[("Excel files", "*.xlsx;*.xlsm;*.xls")]
        )
        if self.files_b:
            self.label_b.config(text=f"已选择: {len(self.files_b)} 个文件")

    def select_files_c(self):
        self.files_c = filedialog.askopenfilenames(
            title="选择多个标准件表",
            filetypes=[("Excel files", "*.xlsx;*.xlsm;*.xls")]
        )
        if self.files_c:
            self.label_c.config(text=f"已选择: {len(self.files_c)} 个文件")

    def select_files_d(self):
        self.files_d = filedialog.askopenfilenames(
            title="选择多个BOM表",
            filetypes=[("Excel files", "*.xlsx;*.xlsm;*.xls")]
        )
        if self.files_d:
            self.label_d.config(text=f"已选择: {len(self.files_d)} 个文件")

    def select_dwg_folder(self):
        self.dwg_folder = filedialog.askdirectory(title="选择DWG文件夹")
        if self.dwg_folder:
            self.label_dwg.config(text=f"已选择: {os.path.basename(self.dwg_folder)}")

    def select_dxf_folder(self):
        self.dxf_folder = filedialog.askdirectory(title="选择DXF输出文件夹")
        if self.dxf_folder:
            self.label_dxf.config(text=f"已选择: {os.path.basename(self.dxf_folder)}")

    def get_keywords_from_sheet2(self, ws_b, column):
        keywords = []
        for row in ws_b.iter_rows(min_row=2, max_col=column + 1):
            keyword = row[column - 1].value
            if keyword:
                keywords.append(str(keyword).strip())
        return list(set(keywords))

    def get_keywords(self, ws_b, source_var, entry_widget, column):
        if source_var.get() == "manual":
            keywords_input = entry_widget.get()
            return [k.strip() for k in keywords_input.split(',') if k.strip()]
        else:
            return self.get_keywords_from_sheet2(ws_b, column)

    def dwg_to_dxf(self, input_path, output_path):
        try:
            teigha_path = r"D:\software\ODA\ODAFileConverter.exe"
            command = [teigha_path, os.path.dirname(input_path), os.path.dirname(output_path), "ACAD2018", "DXF", "0", "1", ""]
            subprocess.run(command)
        except Exception as e:
            print(f"Error converting {input_path}: {str(e)}")

    def load_workbook_xls(self, file_path):
        """使用 xlrd 加载 .xls 文件"""
        workbook = xlrd.open_workbook(file_path)
        return workbook

    def extract_flange_info(self, text):
        """
        从文本中提取法兰信息
        """
        # 清理文本，移除不必要的格式化字符
        cleaned_text = text.replace('\\C2;', '').replace('\\C3;', '').replace('\\H1.25x;', '')\
                          .replace('\\P', '').replace('\\pxqc;', '').replace('\\A1;', '')\
                          .replace('{\C2;', '').replace('{\C3;', '').replace('}', '')\
                          .replace('\\H1.875x;', '').replace('\\H4.375x;', '')\
                          .replace('T', '').replace('X', '').replace(' ', '')

        # 定义正则表达式模式
        patterns = [
            r'(?:FA|A)(\d+)-(\d+)',  # 匹配 FA40-4 或 A40-4 格式
            r'DN(\d+)-(\d+)',        # 匹配 DN250-01 格式
        ]

        results = []
        for pattern in patterns:
            matches = re.findall(pattern, cleaned_text)
            for match in matches:
                if pattern.startswith('(?:FA|A)'):
                    # 对于 FA/A 格式的法兰
                    flange_type = f"FA{match[0]}-{match[1]}"
                    results.append(flange_type)
                elif pattern.startswith('DN'):
                    # 对于 DN 格式的法兰
                    flange_type = f"DN{match[0]}-{match[1]}"
                    results.append(flange_type)

        return results

    def process_dxf_text(self, text, dxf_keywords):
        """
        处理 DXF 文本并返回匹配的法兰信息
        """
        flange_types = self.extract_flange_info(text)
        matches = {}

        for flange in flange_types:
            # 对于每个提取出的法兰类型
            base_type = flange
            if flange.startswith('A'):
                # 如果是以 A 开头，添加 F 前缀
                base_type = 'F' + flange

            # 检查是否匹配任何关键词
            for keyword in dxf_keywords:
                if keyword.upper() == base_type.upper():
                    matches[keyword] = matches.get(keyword, 0) + 1

        return matches
    def process_files(self):
        if not self.file_a:
            messagebox.showerror("错误", "请选择数据储存！")
            return

        if not self.files_b and not self.files_c and not self.files_d and not self.dwg_folder:
            messagebox.showerror("错误", "请至少选择油箱明细表、标准件表、工作簿D或DWG文件夹之一！")
            return

        if self.dwg_folder and not self.dxf_folder:
            messagebox.showerror("错误", "选择DWG文件夹时必须指定DXF输出文件夹！")
            return

        progress_window = tk.Toplevel(self.master)
        progress_window.title("处理进度")
        progress_window.geometry("300x100")
        progress_label = ttk.Label(progress_window, text="正在处理文件...")
        progress_label.pack(pady=20)
        
        try:
            # 打开工作簿
            wb_a = load_workbook(self.file_a, keep_vba=True)
            ws_a = wb_a['Sheet1']
            ws_b = wb_a['Sheet2']
            
            # 清空数据区域但保留格式
            progress_label.config(text="正在清空数据区域...")
            progress_window.update()
            
            # 获取合并单元格的范围
            merged_ranges = ws_a.merged_cells.ranges
            
            # 清空A-D列(1-4列)从第5行开始的数据
            for row in range(5, ws_a.max_row + 1):
                for col in range(1, 5):
                    cell = ws_a.cell(row=row, column=col)
                    # 检查是否是合并单元格的一部分
                    is_merged = False
                    for merged_range in merged_ranges:
                        if (row >= merged_range.min_row and row <= merged_range.max_row and 
                            col >= merged_range.min_col and col <= merged_range.max_col):
                            # 如果是合并单元格的一部分，只修改左上角的主单元格
                            if row == merged_range.min_row and col == merged_range.min_col:
                                ws_a.cell(row=row, column=col).value = None
                                # 清除背景填充
                                ws_a.cell(row=row, column=col).fill = PatternFill(fill_type=None)
                            is_merged = True
                            break
                    
                    # 如果不是合并单元格的一部分，直接清空
                    if not is_merged:
                        cell.value = None
                        # 清除背景填充
                        cell.fill = PatternFill(fill_type=None)
            
            # 清空F-G列(6-7列)从第5行开始的数据
            for row in range(5, ws_a.max_row + 1):
                for col in range(6, 8):
                    cell = ws_a.cell(row=row, column=col)
                    # 检查是否是合并单元格的一部分
                    is_merged = False
                    for merged_range in merged_ranges:
                        if (row >= merged_range.min_row and row <= merged_range.max_row and 
                            col >= merged_range.min_col and col <= merged_range.max_col):
                            # 如果是合并单元格的一部分，只修改左上角的主单元格
                            if row == merged_range.min_row and col == merged_range.min_col:
                                ws_a.cell(row=row, column=col).value = None
                                # 清除背景填充
                                ws_a.cell(row=row, column=col).fill = PatternFill(fill_type=None)
                            is_merged = True
                            break
                    
                    # 如果不是合并单元格的一部分，直接清空
                    if not is_merged:
                        cell.value = None
                        # 清除背景填充
                        cell.fill = PatternFill(fill_type=None)

            # 保存清空后的工作簿
            wb_a.save(self.file_a)
            
            # 重新打开工作簿
            wb_a = load_workbook(self.file_a, keep_vba=True)
            ws_a = wb_a['Sheet1']
            ws_b = wb_a['Sheet2']

            font = Font(name='宋体', size=11)
            alignment = Alignment(horizontal='center', vertical='center')

            workbook_keywords = []
            dxf_keywords = []

            if self.files_b or self.files_c or self.files_d:
                workbook_keywords = self.get_keywords(ws_b, self.keyword_source_b, self.entry_keywords_b, 8)
                if not workbook_keywords:
                    messagebox.showerror("错误", "未找到油箱明细表的有效关键词")
                    return

            if self.dwg_folder:
                dxf_keywords = self.get_keywords(ws_b, self.keyword_source_dxf, self.entry_keywords_dxf, 10)
                if not dxf_keywords:
                    messagebox.showerror("错误", "未找到DXF文件的有效关键词")
                    return

            row = 5

            # 处理工作簿B
            if self.files_b:
                progress_label.config(text="正在处理油箱明细表...")
                progress_window.update()

                for file_b in self.files_b:
                    try:
                        # 使用 pandas 读取工作簿B
                        print(f"正在读取文件: {file_b}")  # 打印文件路径
                        df_b = pd.read_excel(file_b, sheet_name='Sheet1')  # 直接指定工作表名称

                        # 打印工作表内容
                        print(df_b)  # 打印整个DataFrame

                        nrows = df_b.shape[0]  # 获取行数

                        for row_b in range(nrows):
                            d_cell = df_b.iloc[row_b, 3]  # D列
                            c_cell = df_b.iloc[row_b, 2]  # C列
                            g_cell = df_b.iloc[row_b, 6]  # G列

                            if pd.notna(d_cell):  # 检查D列是否非空
                                d_str = str(d_cell)
                                for keyword in workbook_keywords:
                                    if keyword in d_str:
                                        c_str = str(c_cell) if pd.notna(c_cell) else ""
                                        cell = ws_a.cell(row=row, column=1, value=d_str)
                                        cell.font = font
                                        cell.alignment = alignment
                                        cell = ws_a.cell(row=row, column=2, value=c_str)
                                        cell.font = font
                                        cell.alignment = alignment
                                        cell = ws_a.cell(row=row, column=3, value=g_cell)
                                        cell.font = font
                                        cell.alignment = alignment
                                        row += 1
                                        break

                    except Exception as e:
                        print(f"处理文件 {file_b} 时出错: {str(e)}")

            # 处理工作簿C
            if self.files_c:
                progress_label.config(text="正在处理标准件表...")
                progress_window.update()

                for file_c in self.files_c:
                    try:
                        if file_c.endswith('.xls'):
                            wb_c = self.load_workbook_xls(file_c)
                            ws_c_source = wb_c.sheet_by_index(0)
                        else:
                            wb_c = openpyxl.load_workbook(file_c, read_only=True, data_only=True)
                            ws_c_source = wb_c.active

                        for row_c in range(1, ws_c_source.nrows):
                            # 更正列的对应关系
                            name_cell = ws_c_source.cell_value(row_c, 3)     # D列 - 名称
                            model_cell = ws_c_source.cell_value(row_c, 2)    # C列 - 型号
                            quantity_cell = ws_c_source.cell_value(row_c, 6)  # G列 - 数量

                            # 检查名称是否存在
                            if name_cell:
                                name_str = str(name_cell)
                                # 遍历关键词进行匹配
                                for keyword in workbook_keywords:
                                    if keyword in name_str:
                                        # 准备型号数据
                                        model_str = str(model_cell) if model_cell else ""

                                        # 写入名称（第1列）
                                        cell = ws_a.cell(row=row, column=1, value=name_str)
                                        cell.font = font
                                        cell.alignment = alignment

                                        # 写入型号（第2列）
                                        cell = ws_a.cell(row=row, column=2, value=model_str)
                                        cell.font = font
                                        cell.alignment = alignment

                                        # 写入数量（第3列）
                                        cell = ws_a.cell(row=row, column=3, value=quantity_cell)
                                        cell.font = font
                                        cell.alignment = alignment

                                        # 移到下一行
                                        row += 1
                                        break  # 找到匹配后退出关键词循环

                    except Exception as e:
                        print(f"处理文件 {file_c} 时出错: {str(e)}")

            # 处理工作簿D
            if self.files_d:
                progress_label.config(text="正在处理工作簿D...")
                progress_window.update()

                # 获取Sheet2中A列的关键词
                keywords_sheet2 = []
                for sheet2_row in ws_b.iter_rows(min_row=2, max_col=1):
                    if sheet2_row[0].value:
                        keywords_sheet2.append(str(sheet2_row[0].value).strip())

                for file_d in self.files_d:
                    try:
                        print(f"正在读取文件: {file_d}")
                        wb_d = openpyxl.load_workbook(file_d, data_only=True)
                        ws_d = wb_d.active

                        # 从第9行开始处理
                        current_row = row  # 保存当前行号
                        for row_d in range(9, ws_d.max_row + 1):
                            merged_text = None
                            
                            # 检查当前单元格是否在任何合并区域内
                            for merged_range in ws_d.merged_cells.ranges:
                                min_row = merged_range.min_row
                                max_row = merged_range.max_row
                                min_col = merged_range.min_col
                                max_col = merged_range.max_col
                                
                                # 检查当前单元格是否在K-Q列的合并区域内
                                if (row_d >= min_row and row_d <= max_row and 
                                    min_col == 11 and max_col == 17):  # K列是11，Q列是17
                                    merged_text = ws_d.cell(row=min_row, column=11).value
                                    break
                            
                            # 如果不在合并区域内，直接读取K列的值
                            if merged_text is None:
                                merged_text = ws_d.cell(row=row_d, column=11).value

                            if merged_text:
                                merged_text = str(merged_text)
                                # 检查是否包含Sheet2中A列的任何关键词
                                for keyword in keywords_sheet2:
                                    if keyword in merged_text:
                                        # 获取I列的数量
                                        quantity = ws_d.cell(row=row_d, column=9).value  # I列是第9列
                                        
                                        # 写入关键词到Sheet1的B列
                                        cell = ws_a.cell(row=current_row, column=2, value=keyword)
                                        cell.font = font
                                        cell.alignment = alignment
                                        
                                        # 写入数量到Sheet1的C列
                                        if quantity is not None:
                                            cell = ws_a.cell(row=current_row, column=3, value=quantity)
                                            cell.font = font
                                            cell.alignment = alignment
                                        
                                        current_row += 1
                                        break

                        row = current_row  # 更新全局行号

                    except Exception as e:
                        print(f"处理文件 {file_d} 时出错: {str(e)}")
                        print(f"错误详情: {str(e)}")
                        # 打印更详细的错误信息
                        import traceback
                        print(traceback.format_exc())

            # 处理DWG文件
            if self.dwg_folder:
                progress_label.config(text="正在处理DWG文件...")
                progress_window.update()

                for filename in os.listdir(self.dwg_folder):
                    if filename.lower().endswith('.dwg'):
                        file_dwg = os.path.join(self.dwg_folder, filename)
                        output_dxf = os.path.join(self.dxf_folder, os.path.splitext(filename)[0] + '.dxf')
                        self.dwg_to_dxf(file_dwg, output_dxf)

                        try:
                            doc = ezdxf.readfile(output_dxf)
                            model_space = doc.modelspace()
                            dxf_keyword_counts = {}

                            for entity in model_space:
                                if hasattr(entity, 'text'):
                                    text = entity.text
                                    matches = self.process_dxf_text(text, dxf_keywords)

                                    # 更新计数
                                    for keyword, count in matches.items():
                                        dxf_keyword_counts[keyword] = dxf_keyword_counts.get(keyword, 0) + count

                            # 将结果写入Excel
                            for keyword, count in dxf_keyword_counts.items():
                                cell = ws_a.cell(row=row, column=2, value=keyword)
                                cell.font = font
                                cell.alignment = alignment
                                cell = ws_a.cell(row=row, column=3, value=count)
                                cell.font = font
                                cell.alignment = alignment
                                row += 1

                        except Exception as e:
                            print(f"处理DXF文件 {output_dxf} 时出错: {str(e)}")

            progress_label.config(text="正在处理密封规格...")
            progress_window.update()

            # 密封规格匹配
            red_fill = PatternFill(start_color='FFFF0000',
                                 end_color='FFFF0000',
                                 fill_type='solid')
            no_fill = PatternFill(fill_type=None)

            for row in ws_a.iter_rows(min_row=5, max_col=2):
                flange_model = row[1].value
                if flange_model:
                    found_match = False
                    cell = ws_a.cell(row=row[0].row, column=4)

                    for row_b in ws_b.iter_rows(min_row=2, max_col=2):
                        if row_b[0].value == flange_model:
                            cell.value = row_b[1].value
                            cell.font = font
                            cell.alignment = alignment
                            cell.fill = no_fill
                            found_match = True
                            break

                    if not found_match:
                        cell.fill = red_fill

            # ... 前面的代码保持不变 ...

            # 密封规格统计
            seal_spec_counts = Counter()
            for row in ws_a.iter_rows(min_row=5, max_col=4):
                d_cell = row[3].value  # 第4列 (D列) - 密封规格
                c_cell = row[2].value  # 第3列 (C列) - 数量

                if d_cell and c_cell:
                    try:
                        quantity = float(c_cell) if isinstance(c_cell, str) else c_cell
                        seal_spec_counts[str(d_cell)] += quantity
                    except (ValueError, TypeError):
                        print(f"警告：第{row[0].row}行的数量值无效")
                        continue

            # 对密封规格进行排序
            def extract_number(spec):
                # 从规格中提取数字
                numbers = re.findall(r'\d+', str(spec))
                return [int(num) for num in numbers] if numbers else [0]

            # 将计数器转换为列表并排序
            sorted_specs = sorted(seal_spec_counts.items(), key=lambda x: extract_number(x[0]))

            # 写入排序后的结果
            row = 5
            for spec, count in sorted_specs:
                # 写入密封规格
                cell = ws_a.cell(row=row, column=6, value=spec)
                cell.font = font
                cell.alignment = alignment

                # 写入对应数量
                cell = ws_a.cell(row=row, column=7, value=count)
                cell.font = font
                cell.alignment = alignment
                row += 1

            wb_a.save(self.file_a)
            progress_window.destroy()
            messagebox.showinfo("完成", "文件处理完成！")

        except Exception as e:
            progress_window.destroy()
            messagebox.showerror("错误", f"处理过程中出错: {str(e)}")

    def scroll_text(self):
        if not self.scroll_paused:
            self.current_scroll_pos += 0.001
            if self.current_scroll_pos > 1.0:
                self.current_scroll_pos = 0.0
            
            # 检查help_text是否存在
            if self.help_text.winfo_exists():
                self.help_text.yview_moveto(self.current_scroll_pos)
        
        # 安排下一次滚动
        self.after_id = self.master.after(50, self.scroll_text)

    def pause_scroll(self, event):
        self.scroll_paused = True

    def resume_scroll(self, event):
        self.scroll_paused = False
        
    def on_closing(self):
        """Handle window closing event"""
        if self.after_id:
            self.master.after_cancel(self.after_id)
            self.after_id = None
        self.master.destroy()

    def open_bom_tool(self):
        """打开BOM表制作工具"""
        if self.after_id:
            self.master.after_cancel(self.after_id)
            self.after_id = None
            
        self.master.destroy()
        
        try:
            python_executable = sys.executable
            script_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "bom_mapping_ui.py"))
            
            if not os.path.exists(script_path):
                messagebox.showerror("错误", f"找不到BOM工具脚本: {script_path}")
                return
                
            subprocess.Popen([python_executable, script_path])
        except Exception as e:
            messagebox.showerror("错误", f"无法启动BOM表制作工具: {e}")


if __name__ == "__main__":
    try:
        root = tk.Tk()
        app = ExcelToolApp(root)
        root.mainloop()
    except Exception as e:
        print(f"程序启动错误: {str(e)}")
