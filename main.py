import openpyxl
from openpyxl import load_workbook
from openpyxl.styles import <PERSON>gnment, Font, PatternFill
import os
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from PIL import Image, ImageTk
import ezdxf
import subprocess
from collections import Counter
import datetime
import pandas as pd
import xlrd
import re
import sys
def resource_path(relative_path):
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

# 然后在加载图片的地方使用这个函数
logo_img = Image.open(resource_path("logo.png"))
machine_img = Image.open(resource_path("hydraulic_press.png"))


class ExcelToolApp:
    def create_header(self):
        # 设置头部高度
        self.header_frame.configure(height=80)
        self.header_frame.pack_propagate(False)

        header_content = ttk.Frame(self.header_frame, style='Header.TFrame')
        header_content.pack(fill='both', expand=True, padx=30, pady=15)

        # 左侧Logo和公司信息
        left_section = ttk.Frame(header_content, style='Header.TFrame')
        left_section.pack(side='left', fill='y')

        try:
            logo_img = Image.open("logo.png")
            original_width, original_height = logo_img.size
            new_height = 50
            new_width = int(original_width * (new_height / original_height))
            logo_img = logo_img.resize((new_width, new_height), Image.LANCZOS)
            self.logo_photo = ImageTk.PhotoImage(logo_img)
            logo_label = ttk.Label(left_section, image=self.logo_photo, background=self.primary_color)
            logo_label.pack(side='left', padx=(0, 15))
        except Exception as e:
            print(f"Logo加载错误: {str(e)}")
            # 创建一个现代化的Logo替代
            logo_frame = tk.Frame(left_section, bg='white', width=50, height=50)
            logo_frame.pack(side='left', padx=(0, 15))
            logo_frame.pack_propagate(False)
            logo_text = tk.Label(logo_frame, text="天锻",
                               font=('Microsoft YaHei UI', 16, 'bold'),
                               fg=self.primary_color, bg='white')
            logo_text.place(relx=0.5, rely=0.5, anchor='center')

        # 公司名称
        company_frame = ttk.Frame(left_section, style='Header.TFrame')
        company_frame.pack(side='left', fill='y')

        company_label = ttk.Label(
            company_frame,
            text="天锻重工",
            style='Header.TLabel',
            font=('Microsoft YaHei UI', 18, 'bold')
        )
        company_label.pack(anchor='w')

        subtitle_label = ttk.Label(
            company_frame,
            text="专注于重型装备制造",
            style='Header.TLabel',
            font=('Microsoft YaHei UI', 10)
        )
        subtitle_label.pack(anchor='w')

        # 右侧标题和版本信息
        right_section = ttk.Frame(header_content, style='Header.TFrame')
        right_section.pack(side='right', fill='y')

        title_label = ttk.Label(
            right_section,
            text="重型装备数据处理系统",
            style='Header.TLabel',
            font=('Microsoft YaHei UI', 20, 'bold')
        )
        title_label.pack(anchor='e')

        version_label = ttk.Label(
            right_section,
            text="Version 1.0",
            style='Header.TLabel',
            font=('Microsoft YaHei UI', 10)
        )
        version_label.pack(anchor='e')
    def create_side_panel(self):
        # 创建现代化侧边面板
        side_frame = tk.Frame(self.content_area, bg=self.surface_color,
                            relief='solid', bd=1, highlightbackground=self.divider_color)
        side_frame.pack(side='left', fill='y', padx=(0, 15), pady=0)
        side_frame.configure(width=350)
        side_frame.pack_propagate(False)

        try:
            # 图片容器
            image_container = tk.Frame(side_frame, bg=self.surface_color)
            image_container.pack(fill='x', padx=20, pady=20)

            machine_img = Image.open("hydraulic_press.png")
            original_width, original_height = machine_img.size
            new_width = 300
            new_height = int(original_height * (new_width / original_width))
            machine_img = machine_img.resize((new_width, new_height), Image.LANCZOS)
            self.machine_photo = ImageTk.PhotoImage(machine_img)
            machine_label = tk.Label(image_container, image=self.machine_photo,
                                   bg=self.surface_color)
            machine_label.pack()

            # 公司信息卡片
            info_card = tk.Frame(side_frame, bg=self.primary_color,
                               relief='flat', bd=0)
            info_card.pack(fill='x', padx=20, pady=(0, 20))

            company_info = tk.Label(
                info_card,
                text="天锻重工\n专注于重型装备制造\n致力于技术创新",
                font=('Microsoft YaHei UI', 12, 'bold'),
                fg='white',
                bg=self.primary_color,
                justify='center',
                pady=15
            )
            company_info.pack()

            # 使用说明标题
            help_title = tk.Label(side_frame, text="📖 使用说明",
                                font=('Microsoft YaHei UI', 14, 'bold'),
                                bg=self.surface_color, fg=self.primary_color)
            help_title.pack(anchor='w', padx=20, pady=(0, 10))

            # 帮助文本区域
            help_text = """1. 选择文件
• 数据储存：选择"密封明细统计"文件
• 油箱明细表/标准件表/BOM表：可选择多个Excel文件
• DWG文件：选择DWG文件夹和DXF输出文件夹

2. 配置参数
• 选择关键词来源（自动读取或手动输入）
• 根据需要调整相关设置

3. 开始处理
• 点击"开始处理"按钮
• 等待处理完成

4. 查看结果
• Sheet1的A-D列：提取的零件信息
• Sheet1的F-G列：密封规格统计结果
• 红色背景：表示未找到对应密封规格

5. 注意事项
• 使用前请安装ODAFileConverter软件
• 支持格式：Excel(.xlsx/.xlsm/.xls)，图纸(.dwg)
• 处理前会清空数据区域，请注意备份

如有问题请联系：重型装备设计所-胡猛超"""

            # 创建滚动文本框
            text_frame = tk.Frame(side_frame, bg=self.surface_color)
            text_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

            # 添加滚动条
            scrollbar = tk.Scrollbar(text_frame)
            scrollbar.pack(side='right', fill='y')

            self.help_text = tk.Text(text_frame,
                                   wrap=tk.WORD,
                                   font=('Microsoft YaHei UI', 9),
                                   bg=self.surface_color,
                                   fg=self.text_primary,
                                   relief='flat',
                                   bd=0,
                                   yscrollcommand=scrollbar.set)
            self.help_text.pack(fill='both', expand=True)
            scrollbar.config(command=self.help_text.yview)

            self.help_text.insert('1.0', help_text)
            self.help_text.configure(state='disabled')

        except Exception as e:
            print(f"图片加载错误: {str(e)}")
            # 创建备用信息显示
            info_frame = tk.Frame(side_frame, bg=self.surface_color)
            info_frame.pack(fill='both', expand=True, padx=20, pady=20)

            # 显示图标代替图片
            icon_label = tk.Label(info_frame, text="🏭",
                                font=('Microsoft YaHei UI', 48),
                                bg=self.surface_color, fg=self.primary_color)
            icon_label.pack(pady=20)

            company_info = tk.Label(
                info_frame,
                text="天锻重工\n专注于重型装备制造\n致力于技术创新",
                font=('Microsoft YaHei UI', 12, 'bold'),
                fg=self.text_primary,
                bg=self.surface_color,
                justify='center'
            )
            company_info.pack()

    def create_main_content(self):
        self.content_frame = ttk.Frame(self.content_area, style='Content.TFrame')
        self.content_frame.pack(side='right', fill='both', expand=True, padx=10)

        self.create_file_selection_frame()
        self.create_keyword_frame()
        self.create_process_button()
        self.create_bom_button()

    def create_file_selection_frame(self):
        # 创建现代化的文件选择卡片
        file_frame = ttk.LabelFrame(
            self.content_frame,
            text="📁 文件选择",
            padding="20",
            style='Custom.TLabelframe'
        )
        file_frame.pack(fill='x', pady=(0, 15))

        # 文件选择项配置
        file_configs = [
            {"label": "数据储存", "icon": "💾", "var": "file_a", "command": self.select_file_a, "type": "file"},
            {"label": "油箱明细表", "icon": "📊", "var": "files_b", "command": self.select_files_b, "type": "files"},
            {"label": "标准件表", "icon": "🔧", "var": "files_c", "command": self.select_files_c, "type": "files"},
            {"label": "BOM表", "icon": "📋", "var": "files_d", "command": self.select_files_d, "type": "files"},
            {"label": "DWG文件夹", "icon": "📂", "var": "dwg_folder", "command": self.select_dwg_folder, "type": "folder"},
            {"label": "DXF输出文件夹", "icon": "📤", "var": "dxf_folder", "command": self.select_dxf_folder, "type": "folder"}
        ]

        # 创建网格布局
        for i, config in enumerate(file_configs):
            row = i // 2
            col = i % 2

            # 创建文件项容器
            item_frame = tk.Frame(file_frame, bg=self.surface_color, relief='solid',
                                bd=1, highlightbackground=self.divider_color)
            item_frame.grid(row=row, column=col, sticky='ew', padx=5, pady=5)

            # 配置网格权重
            file_frame.grid_columnconfigure(col, weight=1)

            # 图标和标签
            header_frame = tk.Frame(item_frame, bg=self.surface_color)
            header_frame.pack(fill='x', padx=15, pady=(10, 5))

            icon_label = tk.Label(header_frame, text=config["icon"],
                                font=('Microsoft YaHei UI', 16),
                                bg=self.surface_color, fg=self.primary_color)
            icon_label.pack(side='left')

            title_label = tk.Label(header_frame, text=config["label"],
                                 font=('Microsoft YaHei UI', 11, 'bold'),
                                 bg=self.surface_color, fg=self.text_primary)
            title_label.pack(side='left', padx=(8, 0))

            # 状态标签
            status_label = tk.Label(item_frame, text="未选择",
                                  font=('Microsoft YaHei UI', 9),
                                  bg=self.surface_color, fg=self.text_secondary)
            status_label.pack(fill='x', padx=15, pady=(0, 5))

            # 选择按钮
            button_text = "选择文件夹" if config["type"] == "folder" else "选择文件"
            select_button = ttk.Button(
                item_frame,
                text=button_text,
                command=config["command"],
                style='Custom.TButton'
            )
            select_button.pack(fill='x', padx=15, pady=(0, 10))

            # 保存标签引用以便后续更新
            setattr(self, f"label_{config['var']}", status_label)
    def create_keyword_frame(self):
        keyword_frame = ttk.LabelFrame(
            self.content_frame,
            text="⚙️ 参数配置",
            padding="20",
            style='Custom.TLabelframe'
        )
        keyword_frame.pack(fill='x', pady=(0, 15))

        # 创建两列布局
        left_column = tk.Frame(keyword_frame, bg=self.surface_color)
        left_column.pack(side='left', fill='both', expand=True, padx=(0, 10))

        right_column = tk.Frame(keyword_frame, bg=self.surface_color)
        right_column.pack(side='right', fill='both', expand=True, padx=(10, 0))

        # 左列：油箱明细表/标准件表关键词设置
        self.create_keyword_section(left_column,
                                  "📊 油箱明细表/标准件表关键词",
                                  "从Sheet2的H列读取",
                                  "手动输入关键词（多个关键词用逗号分隔）",
                                  self.keyword_source_b,
                                  self.toggle_keyword_input_b,
                                  "entry_keywords_b")

        # 右列：DXF关键词设置
        self.create_keyword_section(right_column,
                                  "📐 DXF文件关键词",
                                  "从Sheet2的J列读取",
                                  "手动输入关键词（多个关键词用逗号分隔）",
                                  self.keyword_source_dxf,
                                  self.toggle_keyword_input_dxf,
                                  "entry_keywords_dxf")

    def create_keyword_section(self, parent, title, auto_text, manual_text, var, toggle_func, entry_attr):
        # 标题
        title_label = tk.Label(parent, text=title,
                             font=('Microsoft YaHei UI', 12, 'bold'),
                             bg=self.surface_color, fg=self.primary_color)
        title_label.pack(anchor='w', pady=(0, 10))

        # 单选按钮容器
        radio_frame = tk.Frame(parent, bg=self.surface_color)
        radio_frame.pack(fill='x', pady=(0, 10))

        # 自动读取选项
        auto_radio = tk.Radiobutton(radio_frame, text=auto_text,
                                  variable=var, value="sheet",
                                  command=toggle_func,
                                  font=('Microsoft YaHei UI', 10),
                                  bg=self.surface_color, fg=self.text_primary,
                                  selectcolor=self.primary_light,
                                  activebackground=self.surface_color)
        auto_radio.pack(anchor='w', pady=2)

        # 手动输入选项
        manual_radio = tk.Radiobutton(radio_frame, text="手动输入",
                                    variable=var, value="manual",
                                    command=toggle_func,
                                    font=('Microsoft YaHei UI', 10),
                                    bg=self.surface_color, fg=self.text_primary,
                                    selectcolor=self.primary_light,
                                    activebackground=self.surface_color)
        manual_radio.pack(anchor='w', pady=2)

        # 输入框容器
        entry_frame = tk.Frame(parent, bg=self.surface_color)
        entry_frame.pack(fill='x', pady=(5, 0))

        # 输入框标签
        entry_label = tk.Label(entry_frame, text=manual_text,
                             font=('Microsoft YaHei UI', 9),
                             bg=self.surface_color, fg=self.text_secondary)
        entry_label.pack(anchor='w', pady=(0, 5))

        # 输入框
        entry = tk.Entry(entry_frame, font=('Microsoft YaHei UI', 10),
                        relief='solid', bd=1, highlightthickness=1,
                        highlightcolor=self.primary_color)
        entry.pack(fill='x')

        # 保存输入框引用
        setattr(self, entry_attr, entry)

    def create_process_button(self):
        # 创建按钮容器
        button_container = tk.Frame(self.content_frame, bg=self.bg_color)
        button_container.pack(fill='x', pady=20)

        # 创建按钮网格
        buttons_frame = tk.Frame(button_container, bg=self.bg_color)
        buttons_frame.pack(expand=True)

        # 主处理按钮
        process_frame = tk.Frame(buttons_frame, bg=self.surface_color, relief='solid',
                               bd=1, highlightbackground=self.divider_color)
        process_frame.pack(pady=10)

        # 按钮图标和文本
        button_content = tk.Frame(process_frame, bg=self.surface_color)
        button_content.pack(padx=30, pady=20)

        # 图标
        icon_label = tk.Label(button_content, text="🚀",
                            font=('Microsoft YaHei UI', 24),
                            bg=self.surface_color)
        icon_label.pack()

        # 主按钮
        self.process_button = tk.Button(
            button_content,
            text="开始处理",
            command=self.process_files,
            font=('Microsoft YaHei UI', 16, 'bold'),
            bg=self.accent_color,
            fg='white',
            relief='flat',
            bd=0,
            padx=40,
            pady=15,
            cursor='hand2'
        )
        self.process_button.pack(pady=(10, 0))

        # 添加按钮悬停效果
        def on_enter(e):
            self.process_button.config(bg='#66BB6A')
        def on_leave(e):
            self.process_button.config(bg=self.accent_color)

        self.process_button.bind("<Enter>", on_enter)
        self.process_button.bind("<Leave>", on_leave)

    def create_bom_button(self):
        # BOM表制作工具按钮容器
        bom_container = tk.Frame(self.content_frame, bg=self.bg_color)
        bom_container.pack(fill='x', pady=(0, 20))

        # BOM按钮框架
        bom_frame = tk.Frame(bom_container, bg=self.surface_color, relief='solid',
                           bd=1, highlightbackground=self.divider_color)
        bom_frame.pack(expand=True)

        # 按钮内容
        bom_content = tk.Frame(bom_frame, bg=self.surface_color)
        bom_content.pack(padx=20, pady=15)

        # 图标
        bom_icon = tk.Label(bom_content, text="📋",
                          font=('Microsoft YaHei UI', 20),
                          bg=self.surface_color)
        bom_icon.pack()

        # BOM按钮
        bom_button = tk.Button(
            bom_content,
            text="BOM表制作工具",
            command=self.open_bom_tool,
            font=('Microsoft YaHei UI', 14, 'bold'),
            bg=self.secondary_color,
            fg='white',
            relief='flat',
            bd=0,
            padx=30,
            pady=12,
            cursor='hand2'
        )
        bom_button.pack(pady=(8, 0))

        # 添加按钮悬停效果
        def on_enter_bom(e):
            bom_button.config(bg='#FF7043')
        def on_leave_bom(e):
            bom_button.config(bg=self.secondary_color)

        bom_button.bind("<Enter>", on_enter_bom)
        bom_button.bind("<Leave>", on_leave_bom)

    def create_status_bar(self):
        # 创建现代化状态栏
        self.status_frame = tk.Frame(self.main_bg, bg=self.surface_color,
                                   relief='solid', bd=1,
                                   highlightbackground=self.divider_color)
        self.status_frame.pack(side='bottom', fill='x')

        # 状态栏内容
        status_content = tk.Frame(self.status_frame, bg=self.surface_color)
        status_content.pack(fill='x', padx=20, pady=8)

        # 左侧时间显示
        time_frame = tk.Frame(status_content, bg=self.surface_color)
        time_frame.pack(side='left')

        time_icon = tk.Label(time_frame, text="🕒",
                           font=('Microsoft YaHei UI', 12),
                           bg=self.surface_color)
        time_icon.pack(side='left', padx=(0, 5))

        self.time_label = tk.Label(
            time_frame,
            text="",
            font=('Microsoft YaHei UI', 10),
            fg=self.text_secondary,
            bg=self.surface_color
        )
        self.time_label.pack(side='left')

        # 右侧版权信息
        copyright_frame = tk.Frame(status_content, bg=self.surface_color)
        copyright_frame.pack(side='right')

        copyright_icon = tk.Label(copyright_frame, text="©",
                                font=('Microsoft YaHei UI', 12, 'bold'),
                                bg=self.surface_color, fg=self.primary_color)
        copyright_icon.pack(side='left', padx=(0, 5))

        self.copyright_label = tk.Label(
            copyright_frame,
            text="版权所有 © 2025 天锻重工",
            font=('Microsoft YaHei UI', 10),
            fg=self.text_secondary,
            bg=self.surface_color
        )
        self.copyright_label.pack(side='left')

        # 启动时间显示
        self.update_time()
    def __init__(self, master):
        self.master = master
        master.title("重型装备数据处理系统")

        # 设置现代化的窗口大小和位置
        screen_width = master.winfo_screenwidth()
        screen_height = master.winfo_screenheight()
        window_width = int(screen_width * 0.8)
        window_height = int(screen_height * 0.8)
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        master.geometry(f"{window_width}x{window_height}+{x}+{y}")

        self.style = ttk.Style()
        self.style.theme_use('clam')

        # 现代化主题色彩 - 采用Material Design风格
        self.primary_color = "#1976D2"      # 主蓝色
        self.primary_light = "#42A5F5"      # 浅蓝色
        self.primary_dark = "#0D47A1"       # 深蓝色
        self.secondary_color = "#FF5722"    # 橙红色
        self.accent_color = "#4CAF50"       # 绿色
        self.bg_color = "#FAFAFA"           # 浅灰背景
        self.surface_color = "#FFFFFF"      # 白色表面
        self.text_primary = "#212121"       # 主文字色
        self.text_secondary = "#757575"     # 次要文字色
        self.divider_color = "#E0E0E0"      # 分割线色
        self.shadow_color = "#00000020"     # 阴影色

        # 配置现代化样式
        self.style.configure('Main.TFrame', background=self.bg_color)
        self.style.configure('Header.TFrame', background=self.primary_color)
        self.style.configure('Content.TFrame',
                           background=self.surface_color,
                           relief='flat')

        # 卡片样式
        self.style.configure('Card.TFrame',
                           background=self.surface_color,
                           relief='solid',
                           borderwidth=1,
                           bordercolor=self.divider_color)

        # 按钮样式
        self.style.configure('Custom.TButton',
                           background=self.primary_color,
                           foreground='white',
                           font=('Microsoft YaHei UI', 10),
                           relief='flat',
                           borderwidth=0,
                           focuscolor='none',
                           padding=(15, 8))

        self.style.map('Custom.TButton',
                      background=[('active', self.primary_light),
                                ('pressed', self.primary_dark)])

        # 头部标签样式
        self.style.configure('Header.TLabel',
                           font=('Microsoft YaHei UI', 14, 'bold'),
                           foreground='white',
                           background=self.primary_color)

        # 标签框架样式
        self.style.configure('Custom.TLabelframe',
                           background=self.surface_color,
                           relief='flat',
                           borderwidth=1,
                           bordercolor=self.divider_color)

        self.style.configure('Custom.TLabelframe.Label',
                           font=('Microsoft YaHei UI', 12, 'bold'),
                           foreground=self.primary_color,
                           background=self.surface_color)

        # 大按钮样式
        self.style.configure('Large.TButton',
                           background=self.accent_color,
                           foreground='white',
                           font=('Microsoft YaHei UI', 14, 'bold'),
                           relief='flat',
                           borderwidth=0,
                           focuscolor='none',
                           padding=(20, 12))

        self.style.map('Large.TButton',
                      background=[('active', '#66BB6A'),
                                ('pressed', '#388E3C')])

        # 次要按钮样式
        self.style.configure('Secondary.TButton',
                           background=self.secondary_color,
                           foreground='white',
                           font=('Microsoft YaHei UI', 12, 'bold'),
                           relief='flat',
                           borderwidth=0,
                           focuscolor='none',
                           padding=(15, 10))

        self.style.map('Secondary.TButton',
                      background=[('active', '#FF7043'),
                                ('pressed', '#D84315')])

        # 创建主框架
        self.main_bg = ttk.Frame(master, style='Main.TFrame')
        self.main_bg.pack(fill='both', expand=True)

        # 创建顶部栏
        self.header_frame = ttk.Frame(self.main_bg, style='Header.TFrame')
        self.header_frame.pack(fill='x', pady=0)

        # 创建内容区域
        self.content_area = ttk.Frame(self.main_bg, style='Main.TFrame')
        self.content_area.pack(fill='both', expand=True, padx=20, pady=10)

        # 初始化变量
        self.file_a = None
        self.files_b = []
        self.files_c = []
        self.files_d = []
        self.dwg_folder = None
        self.dxf_folder = None
        self.keyword_source_b = tk.StringVar(value="sheet")
        self.keyword_source_dxf = tk.StringVar(value="sheet")

        # 创建界面元素
        try:
            self.create_header()
            self.create_side_panel()
            self.create_main_content()
            self.create_status_bar()
        except Exception as e:
            print(f"界面创建错误: {str(e)}")
            messagebox.showerror("界面错误", f"界面创建失败: {str(e)}")

        # Add a protocol handler for closing the window
        self.master.protocol("WM_DELETE_WINDOW", self.on_closing)

    def update_time(self):
        try:
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if hasattr(self, 'time_label') and self.time_label.winfo_exists():
                self.time_label.config(text=f"当前时间：{current_time}")
                self.master.after(1000, self.update_time)
        except Exception as e:
            print(f"时间更新错误: {str(e)}")

    def toggle_keyword_input_b(self):
        if self.keyword_source_b.get() == "manual":
            self.entry_keywords_b.config(state='normal')
        else:
            self.entry_keywords_b.delete(0, tk.END)
            self.entry_keywords_b.config(state='disabled')
        # 初始化时调用一次toggle函数
        self.entry_keywords_b.config(state='disabled')  # 默认禁用输入框

    def toggle_keyword_input_dxf(self):
        if self.keyword_source_dxf.get() == "manual":
            self.entry_keywords_dxf.config(state='normal')
        else:
            self.entry_keywords_dxf.delete(0, tk.END)
            self.entry_keywords_dxf.config(state='disabled')
        # 初始化时调用一次toggle函数
        self.entry_keywords_dxf.config(state='disabled')  # 默认禁用输入框

    def on_closing(self):
        """窗口关闭时的处理"""
        self.master.destroy()
    def select_file_a(self):
        self.file_a = filedialog.askopenfilename(
            title="选择数据储存",
            filetypes=[("Excel files", "*.xlsx;*.xlsm;*.xls")]
        )
        if self.file_a:
            self.label_file_a.config(text=f"✅ 已选择: {os.path.basename(self.file_a)}",
                                   fg=self.accent_color)

    def select_files_b(self):
        self.files_b = filedialog.askopenfilenames(
            title="选择多个油箱明细表",
            filetypes=[("Excel files", "*.xlsx;*.xlsm;*.xls")]
        )
        if self.files_b:
            self.label_files_b.config(text=f"✅ 已选择: {len(self.files_b)} 个文件",
                                    fg=self.accent_color)

    def select_files_c(self):
        self.files_c = filedialog.askopenfilenames(
            title="选择多个标准件表",
            filetypes=[("Excel files", "*.xlsx;*.xlsm;*.xls")]
        )
        if self.files_c:
            self.label_files_c.config(text=f"✅ 已选择: {len(self.files_c)} 个文件",
                                    fg=self.accent_color)

    def select_files_d(self):
        self.files_d = filedialog.askopenfilenames(
            title="选择多个BOM表",
            filetypes=[("Excel files", "*.xlsx;*.xlsm;*.xls")]
        )
        if self.files_d:
            self.label_files_d.config(text=f"✅ 已选择: {len(self.files_d)} 个文件",
                                    fg=self.accent_color)

    def select_dwg_folder(self):
        self.dwg_folder = filedialog.askdirectory(title="选择DWG文件夹")
        if self.dwg_folder:
            self.label_dwg_folder.config(text=f"✅ 已选择: {os.path.basename(self.dwg_folder)}",
                                       fg=self.accent_color)

    def select_dxf_folder(self):
        self.dxf_folder = filedialog.askdirectory(title="选择DXF输出文件夹")
        if self.dxf_folder:
            self.label_dxf_folder.config(text=f"✅ 已选择: {os.path.basename(self.dxf_folder)}",
                                       fg=self.accent_color)

    def get_keywords_from_sheet2(self, ws_b, column):
        keywords = []
        for row in ws_b.iter_rows(min_row=2, max_col=column + 1):
            keyword = row[column - 1].value
            if keyword:
                keywords.append(str(keyword).strip())
        return list(set(keywords))

    def get_keywords(self, ws_b, source_var, entry_widget, column):
        if source_var.get() == "manual":
            keywords_input = entry_widget.get()
            return [k.strip() for k in keywords_input.split(',') if k.strip()]
        else:
            return self.get_keywords_from_sheet2(ws_b, column)

    def dwg_to_dxf(self, input_path, output_path):
        try:
            teigha_path = r"D:\software\ODA\ODAFileConverter.exe"
            command = [teigha_path, os.path.dirname(input_path), os.path.dirname(output_path), "ACAD2018", "DXF", "0", "1", ""]
            subprocess.run(command)
        except Exception as e:
            print(f"Error converting {input_path}: {str(e)}")

    def load_workbook_xls(self, file_path):
        """使用 xlrd 加载 .xls 文件"""
        workbook = xlrd.open_workbook(file_path)
        return workbook

    def extract_flange_info(self, text):
        """
        从文本中提取法兰信息
        """
        # 清理文本，移除不必要的格式化字符
        cleaned_text = text.replace('\\C2;', '').replace('\\C3;', '').replace('\\H1.25x;', '')\
                          .replace('\\P', '').replace('\\pxqc;', '').replace('\\A1;', '')\
                          .replace('{\C2;', '').replace('{\C3;', '').replace('}', '')\
                          .replace('\\H1.875x;', '').replace('\\H4.375x;', '')\
                          .replace('T', '').replace('X', '').replace(' ', '')

        # 定义正则表达式模式
        patterns = [
            r'(?:FA|A)(\d+)-(\d+)',  # 匹配 FA40-4 或 A40-4 格式
            r'DN(\d+)-(\d+)',        # 匹配 DN250-01 格式
        ]

        results = []
        for pattern in patterns:
            matches = re.findall(pattern, cleaned_text)
            for match in matches:
                if pattern.startswith('(?:FA|A)'):
                    # 对于 FA/A 格式的法兰
                    flange_type = f"FA{match[0]}-{match[1]}"
                    results.append(flange_type)
                elif pattern.startswith('DN'):
                    # 对于 DN 格式的法兰
                    flange_type = f"DN{match[0]}-{match[1]}"
                    results.append(flange_type)

        return results

    def process_dxf_text(self, text, dxf_keywords):
        """
        处理 DXF 文本并返回匹配的法兰信息
        """
        flange_types = self.extract_flange_info(text)
        matches = {}

        for flange in flange_types:
            # 对于每个提取出的法兰类型
            base_type = flange
            if flange.startswith('A'):
                # 如果是以 A 开头，添加 F 前缀
                base_type = 'F' + flange

            # 检查是否匹配任何关键词
            for keyword in dxf_keywords:
                if keyword.upper() == base_type.upper():
                    matches[keyword] = matches.get(keyword, 0) + 1

        return matches
    def process_files(self):
        if not self.file_a:
            messagebox.showerror("错误", "请选择数据储存！")
            return

        if not self.files_b and not self.files_c and not self.files_d and not self.dwg_folder:
            messagebox.showerror("错误", "请至少选择油箱明细表、标准件表、工作簿D或DWG文件夹之一！")
            return

        if self.dwg_folder and not self.dxf_folder:
            messagebox.showerror("错误", "选择DWG文件夹时必须指定DXF输出文件夹！")
            return

        progress_window = tk.Toplevel(self.master)
        progress_window.title("处理进度")
        progress_window.geometry("300x100")
        progress_label = ttk.Label(progress_window, text="正在处理文件...")
        progress_label.pack(pady=20)
        
        try:
            # 打开工作簿
            wb_a = load_workbook(self.file_a, keep_vba=True)
            ws_a = wb_a['Sheet1']
            ws_b = wb_a['Sheet2']
            
            # 清空数据区域但保留格式
            progress_label.config(text="正在清空数据区域...")
            progress_window.update()
            
            # 获取合并单元格的范围
            merged_ranges = ws_a.merged_cells.ranges
            
            # 清空A-D列(1-4列)从第5行开始的数据
            for row in range(5, ws_a.max_row + 1):
                for col in range(1, 5):
                    cell = ws_a.cell(row=row, column=col)
                    # 检查是否是合并单元格的一部分
                    is_merged = False
                    for merged_range in merged_ranges:
                        if (row >= merged_range.min_row and row <= merged_range.max_row and 
                            col >= merged_range.min_col and col <= merged_range.max_col):
                            # 如果是合并单元格的一部分，只修改左上角的主单元格
                            if row == merged_range.min_row and col == merged_range.min_col:
                                ws_a.cell(row=row, column=col).value = None
                                # 清除背景填充
                                ws_a.cell(row=row, column=col).fill = PatternFill(fill_type=None)
                            is_merged = True
                            break
                    
                    # 如果不是合并单元格的一部分，直接清空
                    if not is_merged:
                        cell.value = None
                        # 清除背景填充
                        cell.fill = PatternFill(fill_type=None)
            
            # 清空F-G列(6-7列)从第5行开始的数据
            for row in range(5, ws_a.max_row + 1):
                for col in range(6, 8):
                    cell = ws_a.cell(row=row, column=col)
                    # 检查是否是合并单元格的一部分
                    is_merged = False
                    for merged_range in merged_ranges:
                        if (row >= merged_range.min_row and row <= merged_range.max_row and 
                            col >= merged_range.min_col and col <= merged_range.max_col):
                            # 如果是合并单元格的一部分，只修改左上角的主单元格
                            if row == merged_range.min_row and col == merged_range.min_col:
                                ws_a.cell(row=row, column=col).value = None
                                # 清除背景填充
                                ws_a.cell(row=row, column=col).fill = PatternFill(fill_type=None)
                            is_merged = True
                            break
                    
                    # 如果不是合并单元格的一部分，直接清空
                    if not is_merged:
                        cell.value = None
                        # 清除背景填充
                        cell.fill = PatternFill(fill_type=None)

            # 保存清空后的工作簿
            wb_a.save(self.file_a)
            
            # 重新打开工作簿
            wb_a = load_workbook(self.file_a, keep_vba=True)
            ws_a = wb_a['Sheet1']
            ws_b = wb_a['Sheet2']

            font = Font(name='宋体', size=11)
            alignment = Alignment(horizontal='center', vertical='center')

            workbook_keywords = []
            dxf_keywords = []

            if self.files_b or self.files_c or self.files_d:
                workbook_keywords = self.get_keywords(ws_b, self.keyword_source_b, self.entry_keywords_b, 8)
                if not workbook_keywords:
                    messagebox.showerror("错误", "未找到油箱明细表的有效关键词")
                    return

            if self.dwg_folder:
                dxf_keywords = self.get_keywords(ws_b, self.keyword_source_dxf, self.entry_keywords_dxf, 10)
                if not dxf_keywords:
                    messagebox.showerror("错误", "未找到DXF文件的有效关键词")
                    return

            row = 5

            # 处理工作簿B
            if self.files_b:
                progress_label.config(text="正在处理油箱明细表...")
                progress_window.update()

                for file_b in self.files_b:
                    try:
                        # 使用 pandas 读取工作簿B
                        print(f"正在读取文件: {file_b}")  # 打印文件路径
                        df_b = pd.read_excel(file_b, sheet_name='Sheet1')  # 直接指定工作表名称

                        # 打印工作表内容
                        print(df_b)  # 打印整个DataFrame

                        nrows = df_b.shape[0]  # 获取行数

                        for row_b in range(nrows):
                            d_cell = df_b.iloc[row_b, 3]  # D列
                            c_cell = df_b.iloc[row_b, 2]  # C列
                            g_cell = df_b.iloc[row_b, 6]  # G列

                            if pd.notna(d_cell):  # 检查D列是否非空
                                d_str = str(d_cell)
                                for keyword in workbook_keywords:
                                    if keyword in d_str:
                                        c_str = str(c_cell) if pd.notna(c_cell) else ""
                                        cell = ws_a.cell(row=row, column=1, value=d_str)
                                        cell.font = font
                                        cell.alignment = alignment
                                        cell = ws_a.cell(row=row, column=2, value=c_str)
                                        cell.font = font
                                        cell.alignment = alignment
                                        cell = ws_a.cell(row=row, column=3, value=g_cell)
                                        cell.font = font
                                        cell.alignment = alignment
                                        row += 1
                                        break

                    except Exception as e:
                        print(f"处理文件 {file_b} 时出错: {str(e)}")

            # 处理工作簿C
            if self.files_c:
                progress_label.config(text="正在处理标准件表...")
                progress_window.update()

                for file_c in self.files_c:
                    try:
                        if file_c.endswith('.xls'):
                            wb_c = self.load_workbook_xls(file_c)
                            ws_c_source = wb_c.sheet_by_index(0)
                        else:
                            wb_c = openpyxl.load_workbook(file_c, read_only=True, data_only=True)
                            ws_c_source = wb_c.active

                        for row_c in range(1, ws_c_source.nrows):
                            # 更正列的对应关系
                            name_cell = ws_c_source.cell_value(row_c, 3)     # D列 - 名称
                            model_cell = ws_c_source.cell_value(row_c, 2)    # C列 - 型号
                            quantity_cell = ws_c_source.cell_value(row_c, 6)  # G列 - 数量

                            # 检查名称是否存在
                            if name_cell:
                                name_str = str(name_cell)
                                # 遍历关键词进行匹配
                                for keyword in workbook_keywords:
                                    if keyword in name_str:
                                        # 准备型号数据
                                        model_str = str(model_cell) if model_cell else ""

                                        # 写入名称（第1列）
                                        cell = ws_a.cell(row=row, column=1, value=name_str)
                                        cell.font = font
                                        cell.alignment = alignment

                                        # 写入型号（第2列）
                                        cell = ws_a.cell(row=row, column=2, value=model_str)
                                        cell.font = font
                                        cell.alignment = alignment

                                        # 写入数量（第3列）
                                        cell = ws_a.cell(row=row, column=3, value=quantity_cell)
                                        cell.font = font
                                        cell.alignment = alignment

                                        # 移到下一行
                                        row += 1
                                        break  # 找到匹配后退出关键词循环

                    except Exception as e:
                        print(f"处理文件 {file_c} 时出错: {str(e)}")

            # 处理工作簿D
            if self.files_d:
                progress_label.config(text="正在处理工作簿D...")
                progress_window.update()

                # 获取Sheet2中A列的关键词
                keywords_sheet2 = []
                for sheet2_row in ws_b.iter_rows(min_row=2, max_col=1):
                    if sheet2_row[0].value:
                        keywords_sheet2.append(str(sheet2_row[0].value).strip())

                for file_d in self.files_d:
                    try:
                        print(f"正在读取文件: {file_d}")
                        wb_d = openpyxl.load_workbook(file_d, data_only=True)
                        ws_d = wb_d.active

                        # 从第9行开始处理
                        current_row = row  # 保存当前行号
                        for row_d in range(9, ws_d.max_row + 1):
                            merged_text = None
                            
                            # 检查当前单元格是否在任何合并区域内
                            for merged_range in ws_d.merged_cells.ranges:
                                min_row = merged_range.min_row
                                max_row = merged_range.max_row
                                min_col = merged_range.min_col
                                max_col = merged_range.max_col
                                
                                # 检查当前单元格是否在K-Q列的合并区域内
                                if (row_d >= min_row and row_d <= max_row and 
                                    min_col == 11 and max_col == 17):  # K列是11，Q列是17
                                    merged_text = ws_d.cell(row=min_row, column=11).value
                                    break
                            
                            # 如果不在合并区域内，直接读取K列的值
                            if merged_text is None:
                                merged_text = ws_d.cell(row=row_d, column=11).value

                            if merged_text:
                                merged_text = str(merged_text)
                                # 检查是否包含Sheet2中A列的任何关键词
                                for keyword in keywords_sheet2:
                                    if keyword in merged_text:
                                        # 获取I列的数量
                                        quantity = ws_d.cell(row=row_d, column=9).value  # I列是第9列
                                        
                                        # 写入关键词到Sheet1的B列
                                        cell = ws_a.cell(row=current_row, column=2, value=keyword)
                                        cell.font = font
                                        cell.alignment = alignment
                                        
                                        # 写入数量到Sheet1的C列
                                        if quantity is not None:
                                            cell = ws_a.cell(row=current_row, column=3, value=quantity)
                                            cell.font = font
                                            cell.alignment = alignment
                                        
                                        current_row += 1
                                        break

                        row = current_row  # 更新全局行号

                    except Exception as e:
                        print(f"处理文件 {file_d} 时出错: {str(e)}")
                        print(f"错误详情: {str(e)}")
                        # 打印更详细的错误信息
                        import traceback
                        print(traceback.format_exc())

            # 处理DWG文件
            if self.dwg_folder:
                progress_label.config(text="正在处理DWG文件...")
                progress_window.update()

                for filename in os.listdir(self.dwg_folder):
                    if filename.lower().endswith('.dwg'):
                        file_dwg = os.path.join(self.dwg_folder, filename)
                        output_dxf = os.path.join(self.dxf_folder, os.path.splitext(filename)[0] + '.dxf')
                        self.dwg_to_dxf(file_dwg, output_dxf)

                        try:
                            doc = ezdxf.readfile(output_dxf)
                            model_space = doc.modelspace()
                            dxf_keyword_counts = {}

                            for entity in model_space:
                                if hasattr(entity, 'text'):
                                    text = entity.text
                                    matches = self.process_dxf_text(text, dxf_keywords)

                                    # 更新计数
                                    for keyword, count in matches.items():
                                        dxf_keyword_counts[keyword] = dxf_keyword_counts.get(keyword, 0) + count

                            # 将结果写入Excel
                            for keyword, count in dxf_keyword_counts.items():
                                cell = ws_a.cell(row=row, column=2, value=keyword)
                                cell.font = font
                                cell.alignment = alignment
                                cell = ws_a.cell(row=row, column=3, value=count)
                                cell.font = font
                                cell.alignment = alignment
                                row += 1

                        except Exception as e:
                            print(f"处理DXF文件 {output_dxf} 时出错: {str(e)}")

            progress_label.config(text="正在处理密封规格...")
            progress_window.update()

            # 密封规格匹配
            red_fill = PatternFill(start_color='FFFF0000',
                                 end_color='FFFF0000',
                                 fill_type='solid')
            no_fill = PatternFill(fill_type=None)

            for row in ws_a.iter_rows(min_row=5, max_col=2):
                flange_model = row[1].value
                if flange_model:
                    found_match = False
                    cell = ws_a.cell(row=row[0].row, column=4)

                    for row_b in ws_b.iter_rows(min_row=2, max_col=2):
                        if row_b[0].value == flange_model:
                            cell.value = row_b[1].value
                            cell.font = font
                            cell.alignment = alignment
                            cell.fill = no_fill
                            found_match = True
                            break

                    if not found_match:
                        cell.fill = red_fill

            # ... 前面的代码保持不变 ...

            # 密封规格统计
            seal_spec_counts = Counter()
            for row in ws_a.iter_rows(min_row=5, max_col=4):
                d_cell = row[3].value  # 第4列 (D列) - 密封规格
                c_cell = row[2].value  # 第3列 (C列) - 数量

                if d_cell and c_cell:
                    try:
                        quantity = float(c_cell) if isinstance(c_cell, str) else c_cell
                        seal_spec_counts[str(d_cell)] += quantity
                    except (ValueError, TypeError):
                        print(f"警告：第{row[0].row}行的数量值无效")
                        continue

            # 对密封规格进行排序
            def extract_number(spec):
                # 从规格中提取数字
                numbers = re.findall(r'\d+', str(spec))
                return [int(num) for num in numbers] if numbers else [0]

            # 将计数器转换为列表并排序
            sorted_specs = sorted(seal_spec_counts.items(), key=lambda x: extract_number(x[0]))

            # 写入排序后的结果
            row = 5
            for spec, count in sorted_specs:
                # 写入密封规格
                cell = ws_a.cell(row=row, column=6, value=spec)
                cell.font = font
                cell.alignment = alignment

                # 写入对应数量
                cell = ws_a.cell(row=row, column=7, value=count)
                cell.font = font
                cell.alignment = alignment
                row += 1

            wb_a.save(self.file_a)
            progress_window.destroy()
            messagebox.showinfo("完成", "文件处理完成！")

        except Exception as e:
            progress_window.destroy()
            messagebox.showerror("错误", f"处理过程中出错: {str(e)}")

    def scroll_text(self):
        if not self.scroll_paused:
            self.current_scroll_pos += 0.001
            if self.current_scroll_pos > 1.0:
                self.current_scroll_pos = 0.0
            
            # 检查help_text是否存在
            if self.help_text.winfo_exists():
                self.help_text.yview_moveto(self.current_scroll_pos)
        
        # 安排下一次滚动
        self.after_id = self.master.after(50, self.scroll_text)

    def pause_scroll(self, event):
        self.scroll_paused = True

    def resume_scroll(self, event):
        self.scroll_paused = False
        
    def on_closing(self):
        """Handle window closing event"""
        if self.after_id:
            self.master.after_cancel(self.after_id)
            self.after_id = None
        self.master.destroy()

    def open_bom_tool(self):
        """打开BOM表制作工具"""
        if self.after_id:
            self.master.after_cancel(self.after_id)
            self.after_id = None
            
        self.master.destroy()
        
        try:
            python_executable = sys.executable
            script_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "bom_mapping_ui.py"))
            
            if not os.path.exists(script_path):
                messagebox.showerror("错误", f"找不到BOM工具脚本: {script_path}")
                return
                
            subprocess.Popen([python_executable, script_path])
        except Exception as e:
            messagebox.showerror("错误", f"无法启动BOM表制作工具: {e}")


if __name__ == "__main__":
    try:
        root = tk.Tk()
        app = ExcelToolApp(root)
        root.mainloop()
    except Exception as e:
        print(f"程序启动错误: {str(e)}")
