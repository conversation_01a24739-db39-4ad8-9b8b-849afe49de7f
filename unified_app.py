import os
import sys
import tkinter as tk
from tkinter import ttk, PhotoImage, messagebox
from PIL import Image, ImageTk, ImageFilter, ImageEnhance
import random
import time
from threading import Thread
import math
from pathlib import Path

# 尝试导入必要的模块，如果失败则提供友好的错误信息
bom_mapping = None
try:
    import bom_mapping
except ImportError as e:
    module_name = str(e).split("'")[-2] if "'" in str(e) else str(e)
    print(f"导入bom_mapping模块失败: {str(e)}")

# 尝试导入equipment_data_system模块
equipment_data_system = None
try:
    import main as equipment_data_system
except ImportError as e:
    module_name = str(e).split("'")[-2] if "'" in str(e) else str(e)
    print(f"导入main模块失败: {str(e)}")
    
    # 尝试修复xlrd导入问题
    if "xlrd" in str(e):
        try:
            import pip
            print("尝试安装xlrd...")
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "xlrd"])
            print("xlrd安装完成，尝试重新导入main模块...")
            import main as equipment_data_system
        except Exception as e2:
            print(f"安装xlrd或重新导入main模块失败: {str(e2)}")
    
    # 尝试修复ezdxf导入问题
    elif "ezdxf" in str(e):
        try:
            import pip
            print("尝试安装ezdxf...")
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "ezdxf"])
            print("ezdxf安装完成，尝试重新导入main模块...")
            import main as equipment_data_system
        except Exception as e2:
            print(f"安装ezdxf或重新导入main模块失败: {str(e2)}")

def resource_path(relative_path):
    """获取资源的绝对路径，适用于开发环境和PyInstaller打包后的环境"""
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

class ParticleEffect:
    """粒子效果类，用于创建动态背景效果"""
    def __init__(self, canvas, width, height, color_scheme):
        self.canvas = canvas
        self.width = width
        self.height = height
        self.color_scheme = color_scheme
        self.particles = []
        self.max_particles = 50
        self.running = True
        
    def create_particle(self):
        """创建一个新粒子"""
        x = random.randint(0, self.width)
        y = random.randint(0, self.height)
        size = random.randint(2, 6)
        speed = random.uniform(0.5, 2.0)
        angle = random.uniform(0, 2 * math.pi)
        color = random.choice(self.color_scheme)
        
        particle_id = self.canvas.create_oval(
            x - size, y - size, x + size, y + size,
            fill=color, outline='', tags="particle"
        )
        
        self.particles.append({
            'id': particle_id,
            'x': x,
            'y': y,
            'size': size,
            'speed': speed,
            'angle': angle,
            'color': color,
            'alpha': 1.0
        })
    
    def update_particles(self):
        """更新所有粒子的位置和状态"""
        if not self.running:
            return
            
        # 如果粒子数量少于最大值，添加新粒子
        if len(self.particles) < self.max_particles and random.random() < 0.1:
            self.create_particle()
        
        # 更新所有粒子
        particles_to_remove = []
        for particle in self.particles:
            # 移动粒子
            dx = particle['speed'] * math.cos(particle['angle'])
            dy = particle['speed'] * math.sin(particle['angle'])
            self.canvas.move(particle['id'], dx, dy)
            
            # 更新粒子位置
            particle['x'] += dx
            particle['y'] += dy
            
            # 减小透明度
            particle['alpha'] -= 0.005
            if particle['alpha'] <= 0:
                particles_to_remove.append(particle)
                continue
                
            # 检查是否超出边界
            if (particle['x'] < -particle['size'] or 
                particle['x'] > self.width + particle['size'] or
                particle['y'] < -particle['size'] or 
                particle['y'] > self.height + particle['size']):
                particles_to_remove.append(particle)
        
        # 删除需要移除的粒子
        for particle in particles_to_remove:
            self.canvas.delete(particle['id'])
            self.particles.remove(particle)
            
        # 继续动画循环
        self.canvas.after(30, self.update_particles)
    
    def start(self):
        """开始粒子动画"""
        self.running = True
        self.update_particles()
    
    def stop(self):
        """停止粒子动画"""
        self.running = False
        for particle in self.particles:
            self.canvas.delete(particle['id'])
        self.particles = []

class AnimatedButton(tk.Canvas):
    """带有动画效果的按钮"""
    def __init__(self, master, text, command, width=200, height=60, 
                 bg_color="#2C3E50", hover_color="#34495E", text_color="white", 
                 font=("Microsoft YaHei UI", 12, "bold"), corner_radius=10, **kwargs):
        super().__init__(master, width=width, height=height, 
                         bg=master["bg"], highlightthickness=0, **kwargs)
        
        self.bg_color = bg_color
        self.hover_color = hover_color
        self.text_color = text_color
        self.corner_radius = corner_radius
        self.command = command
        self.text = text
        self.width = width
        self.height = height
        self.font = font
        
        # 创建按钮背景
        self.bg_rect = self.create_rounded_rect(0, 0, width, height, corner_radius, fill=bg_color)
        
        # 创建文本
        self.text_id = self.create_text(width/2, height/2, text=text, 
                                        fill=text_color, font=font)
        
        # 绑定事件
        self.bind("<Enter>", self.on_enter)
        self.bind("<Leave>", self.on_leave)
        self.bind("<Button-1>", self.on_click)
        self.bind("<ButtonRelease-1>", self.on_release)
        
        # 创建波纹效果
        self.ripples = []
        
    def create_rounded_rect(self, x1, y1, x2, y2, r, **kwargs):
        """创建圆角矩形"""
        points = [
            x1+r, y1,
            x2-r, y1,
            x2, y1,
            x2, y1+r,
            x2, y2-r,
            x2, y2,
            x2-r, y2,
            x1+r, y2,
            x1, y2,
            x1, y2-r,
            x1, y1+r,
            x1, y1
        ]
        return self.create_polygon(points, smooth=True, **kwargs)
    
    def on_enter(self, event):
        """鼠标进入按钮区域"""
        self.itemconfig(self.bg_rect, fill=self.hover_color)
        self.config(cursor="hand2")
        
    def on_leave(self, event):
        """鼠标离开按钮区域"""
        self.itemconfig(self.bg_rect, fill=self.bg_color)
        self.config(cursor="")
        
    def on_click(self, event):
        """鼠标点击按钮"""
        # 创建波纹效果
        x, y = event.x, event.y
        ripple = self.create_oval(x-5, y-5, x+5, y+5, 
                                 fill="#ffffff", outline="")
        self.ripples.append(ripple)
        self.animate_ripple(ripple, x, y)
        
        # 按钮下沉效果
        self.move(self.text_id, 1, 1)
        
    def on_release(self, event):
        """鼠标释放按钮"""
        self.move(self.text_id, -1, -1)
        if self.command:
            self.command()
            
    def animate_ripple(self, ripple_id, center_x, center_y):
        """动画显示波纹效果"""
        max_size = max(self.width, self.height) * 2
        current_size = 10
        step = 10
        alpha = 0.3
        
        def _animate():
            nonlocal current_size, alpha
            if current_size < max_size and ripple_id in self.find_all():
                # 增加波纹大小
                self.coords(ripple_id, 
                           center_x - current_size/2, 
                           center_y - current_size/2,
                           center_x + current_size/2, 
                           center_y + current_size/2)
                
                # 降低透明度（通过调整颜色亮度）
                alpha -= 0.01
                if alpha > 0:
                    # 使用灰度值代替透明度
                    gray_value = int(255 * (1 - alpha))
                    color = f"#{gray_value:02x}{gray_value:02x}{gray_value:02x}"
                    self.itemconfig(ripple_id, fill=color)
                else:
                    # 完全透明时使用背景色
                    self.itemconfig(ripple_id, fill=self.bg_color)
                
                current_size += step
                self.after(10, _animate)
            else:
                # 动画结束，删除波纹
                if ripple_id in self.find_all():
                    self.delete(ripple_id)
                if ripple_id in self.ripples:
                    self.ripples.remove(ripple_id)
        
        _animate()

class UnifiedApp:
    def __init__(self, root):
        self.root = root
        self.root.title("天锻重工数据处理系统")
        
        # 获取屏幕尺寸
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        
        # 设置窗口大小为屏幕的80%
        window_width = int(screen_width * 0.8)
        window_height = int(screen_height * 0.8)
        
        # 设置窗口位置为屏幕中央
        x_position = (screen_width - window_width) // 2
        y_position = (screen_height - window_height) // 2
        
        self.root.geometry(f"{window_width}x{window_height}+{x_position}+{y_position}")
        
        # 设置主题色
        self.primary_color = "#1E88E5"  # 蓝色
        self.secondary_color = "#FFC107"  # 黄色
        self.bg_color = "#F5F5F5"  # 浅灰色背景
        self.text_color = "#212121"  # 深灰色文字
        
        # 设置样式
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # 配置样式
        self.style.configure('TFrame', background=self.bg_color)
        self.style.configure('TLabel', background=self.bg_color, foreground=self.text_color)
        self.style.configure('TButton', background=self.primary_color, foreground='white')
        
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill='both', expand=True)
        
        # 创建启动界面
        self.create_splash_screen()
        
    def create_splash_screen(self):
        """创建启动界面"""
        # 清除主框架中的所有组件
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        
        # 创建背景画布
        self.splash_canvas = tk.Canvas(
            self.main_frame, 
            bg="#0A1929",
            highlightthickness=0
        )
        self.splash_canvas.pack(fill='both', expand=True)
        
        # 获取画布尺寸
        self.root.update()
        canvas_width = self.splash_canvas.winfo_width()
        canvas_height = self.splash_canvas.winfo_height()
        
        # 创建粒子效果
        color_scheme = ["#3498DB", "#2980B9", "#1ABC9C", "#16A085", "#27AE60"]
        self.particle_effect = ParticleEffect(
            self.splash_canvas, canvas_width, canvas_height, color_scheme
        )
        self.particle_effect.start()
        
        # 添加标题
        title_text = self.splash_canvas.create_text(
            canvas_width // 2,
            canvas_height // 4,
            text="天锻重工数据处理系统",
            font=("Microsoft YaHei UI", 36, "bold"),
            fill="#FFFFFF"
        )
        
        # 添加副标题
        subtitle_text = self.splash_canvas.create_text(
            canvas_width // 2,
            canvas_height // 4 + 60,
            text="专注于重型装备制造 · 致力于技术创新",
            font=("Microsoft YaHei UI", 16),
            fill="#AAAAAA"
        )
        
        # 创建按钮容器
        button_frame = tk.Frame(self.splash_canvas, bg="#0A1929")
        button_window = self.splash_canvas.create_window(
            canvas_width // 2,
            canvas_height // 2 + 50,
            window=button_frame
        )
        
        # 创建两个动画按钮
        bom_button = AnimatedButton(
            button_frame,
            text="BOM表制作工具",
            command=self.open_bom_mapping,
            width=300,
            height=80,
            bg_color="#1976D2",
            hover_color="#2196F3",
            text_color="white",
            font=("Microsoft YaHei UI", 16, "bold"),
            corner_radius=15
        )
        bom_button.pack(pady=20)
        
        equipment_button = AnimatedButton(
            button_frame,
            text="重型装备数据处理系统",
            command=self.open_equipment_system,
            width=300,
            height=80,
            bg_color="#D32F2F",
            hover_color="#F44336",
            text_color="white",
            font=("Microsoft YaHei UI", 16, "bold"),
            corner_radius=15
        )
        equipment_button.pack(pady=20)
        
        # 添加版本信息
        version_text = self.splash_canvas.create_text(
            canvas_width // 2,
            canvas_height - 50,
            text="Version 2.0 · 2025 © 天锻重工",
            font=("Microsoft YaHei UI", 10),
            fill="#AAAAAA"
        )
        
        # 添加动画效果
        self.animate_splash_elements(title_text, subtitle_text, button_window, version_text)
    
    def animate_splash_elements(self, title_text, subtitle_text, button_window, version_text):
        """为启动界面元素添加动画效果"""
        # 初始状态
        self.splash_canvas.itemconfig(title_text, fill="#0A1929")  # 隐藏标题
        self.splash_canvas.itemconfig(subtitle_text, fill="#0A1929")  # 隐藏副标题
        self.splash_canvas.itemconfig(version_text, fill="#0A1929")  # 隐藏版本信息
        self.splash_canvas.coords(button_window, 
                                 self.splash_canvas.winfo_width() // 2, 
                                 self.splash_canvas.winfo_height() + 100)  # 按钮在屏幕外
        
        # 标题动画
        def animate_title(step=0):
            if step <= 10:
                # 使用插值计算颜色
                r = int(10 + 245 * step / 10)  # 从暗到亮
                g = int(25 + 230 * step / 10)
                b = int(41 + 214 * step / 10)
                color = f"#{r:02x}{g:02x}{b:02x}"
                self.splash_canvas.itemconfig(title_text, fill=color)
                self.root.after(50, lambda: animate_title(step + 1))
            else:
                self.splash_canvas.itemconfig(title_text, fill="#FFFFFF")
                # 标题显示完成后，开始副标题动画
                animate_subtitle()
        
        # 副标题动画
        def animate_subtitle(step=0):
            if step <= 10:
                # 使用插值计算颜色
                r = int(10 + 160 * step / 10)  # 从暗到亮
                g = int(25 + 145 * step / 10)
                b = int(41 + 128 * step / 10)
                color = f"#{r:02x}{g:02x}{b:02x}"
                self.splash_canvas.itemconfig(subtitle_text, fill=color)
                self.root.after(50, lambda: animate_subtitle(step + 1))
            else:
                self.splash_canvas.itemconfig(subtitle_text, fill="#AAAAAA")
                # 副标题显示完成后，开始按钮动画
                animate_buttons()
        
        # 按钮动画
        def animate_buttons():
            target_y = self.splash_canvas.winfo_height() // 2 + 50
            current_y = self.splash_canvas.winfo_height() + 100
            animate_button_position(current_y, target_y)
        
        def animate_button_position(current_y, target_y):
            if current_y > target_y:
                new_y = current_y - 10
                self.splash_canvas.coords(button_window, 
                                         self.splash_canvas.winfo_width() // 2, 
                                         new_y)
                self.root.after(10, lambda: animate_button_position(new_y, target_y))
            else:
                # 按钮动画完成后，显示版本信息
                animate_version()
        
        # 版本信息动画
        def animate_version(step=0):
            if step <= 10:
                # 使用插值计算颜色
                r = int(10 + 160 * step / 10)  # 从暗到亮
                g = int(25 + 145 * step / 10)
                b = int(41 + 128 * step / 10)
                color = f"#{r:02x}{g:02x}{b:02x}"
                self.splash_canvas.itemconfig(version_text, fill=color)
                self.root.after(50, lambda: animate_version(step + 1))
            else:
                self.splash_canvas.itemconfig(version_text, fill="#AAAAAA")
        
        # 开始动画序列
        animate_title()
    
    def open_bom_mapping(self):
        """打开BOM表制作工具"""
        # 停止粒子效果
        if hasattr(self, 'particle_effect'):
            self.particle_effect.stop()
        
        # 检查模块是否可用
        if bom_mapping is None:
            messagebox.showerror("模块错误", "无法启动BOM表制作工具，缺少必要的模块。请确保已安装所有依赖。")
            return
        
        # 创建过渡动画
        self.create_transition_animation(lambda: self.launch_bom_mapping_app())
    
    def open_equipment_system(self):
        """打开重型装备数据处理系统"""
        # 停止粒子效果
        if hasattr(self, 'particle_effect'):
            self.particle_effect.stop()
        
        # 检查模块是否可用
        if equipment_data_system is None:
            messagebox.showerror("模块错误", "无法启动重型装备数据处理系统，缺少必要的模块。请确保已安装所有依赖。")
            return
        
        # 创建过渡动画
        self.create_transition_animation(lambda: self.launch_equipment_system_app())
    
    def create_transition_animation(self, callback):
        """创建过渡动画"""
        # 获取画布尺寸
        canvas_width = self.splash_canvas.winfo_width()
        canvas_height = self.splash_canvas.winfo_height()
        
        # 创建覆盖层
        overlay = self.splash_canvas.create_rectangle(
            0, 0, canvas_width, 0,
            fill="#0A1929",
            outline=""
        )
        
        # 动画函数
        def animate_overlay(height=0):
            if height <= canvas_height:
                self.splash_canvas.coords(overlay, 0, 0, canvas_width, height)
                self.root.after(10, lambda: animate_overlay(height + 20))
            else:
                # 动画完成后调用回调函数
                callback()
        
        # 开始动画
        animate_overlay()
    
    def launch_bom_mapping_app(self):
        """启动BOM表制作工具"""
        # 清除主窗口内容
        for widget in self.root.winfo_children():
            widget.destroy()
        
        try:
            # 导入现代化的BOM表制作工具界面
            try:
                # 检查文件是否存在
                if os.path.exists("bom_mapping_ui.py"):
                    from bom_mapping_ui import ModernBomMappingUI
                    # 使用新的现代化界面，并传入返回主菜单的回调函数
                    bom_app = ModernBomMappingUI(self.root, return_callback=self.restart_app)
                else:
                    raise ImportError("找不到bom_mapping_ui.py文件")
            except ImportError as e:
                print(f"导入ModernBomMappingUI失败: {str(e)}")
                # 如果导入失败，使用原始界面
                if bom_mapping is not None:
                    bom_app = bom_mapping.BomMappingApp(self.root)
                    
                    # 添加返回按钮
                    return_button = ttk.Button(
                        self.root,
                        text="返回主菜单",
                        command=self.restart_app
                    )
                    return_button.place(x=10, y=10)
                else:
                    raise Exception("无法导入bom_mapping模块")
        except Exception as e:
            messagebox.showerror("错误", f"启动BOM表制作工具时出错：{str(e)}")
            self.restart_app()
    
    def launch_equipment_system_app(self):
        """启动重型装备数据处理系统"""
        # 清除主窗口内容
        for widget in self.root.winfo_children():
            widget.destroy()
        
        try:
            # 创建重型装备数据处理系统实例
            equipment_app = equipment_data_system.ExcelToolApp(self.root)
            
            # 添加返回按钮
            return_button = ttk.Button(
                self.root,
                text="返回主菜单",
                command=self.restart_app
            )
            return_button.place(x=10, y=10)
        except Exception as e:
            messagebox.showerror("错误", f"启动重型装备数据处理系统时出错：{str(e)}")
            self.restart_app()
    
    def restart_app(self):
        """重启应用，返回启动界面"""
        # 清除主窗口内容
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # 重新创建主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill='both', expand=True)
        
        # 重新创建启动界面
        self.create_splash_screen()

def main():
    # 创建根窗口
    root = tk.Tk()
    
    # 创建应用实例
    app = UnifiedApp(root)
    
    # 运行主循环
    root.mainloop()

if __name__ == "__main__":
    main() 