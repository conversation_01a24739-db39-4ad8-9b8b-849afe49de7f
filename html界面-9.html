<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BOM表制作工具</title>
    <style>
        :root {
            --primary-color: #1E3A8A;
            --primary-light: #3B82F6;
            --accent-color: #0EA5E9;
            --bg-color: #F8FAFC;
            --card-bg: #FFFFFF;
            --text-color: #1E293B;
            --text-light: #64748B;
            --border-color: #E2E8F0;
            --success-color: #10B981;
            --error-color: #EF4444;
            --warning-color: #F59E0B;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei UI", "Segoe UI", sans-serif;
        }
        
        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.5;
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .app-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        /* 顶部区域 */
        .header {
            background: linear-gradient(135deg, var(--primary-color), #2563EB);
            color: white;
            padding: 0.6rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo-area {
            display: flex;
            align-items: center;
        }
        
        .logo {
            width: 32px;
            height: 32px;
            background-color: white;
            border-radius: 6px;
            margin-right: 0.8rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: var(--primary-color);
            font-size: 0.8rem;
        }
        
        .title {
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .subtitle {
            font-size: 0.75rem;
            opacity: 0.9;
        }
        
        .steps-indicator {
            display: flex;
            align-items: center;
            margin: 0 1.5rem;
            flex-grow: 1;
            max-width: 400px;
        }
        
        .step {
            display: flex;
            align-items: center;
            flex-grow: 1;
            position: relative;
        }
        
        .step:not(:last-child)::after {
            content: "";
            display: block;
            height: 2px;
            background-color: rgba(255, 255, 255, 0.3);
            flex-grow: 1;
            margin: 0 0.5rem;
        }
        
        .step.active:not(:last-child)::after {
            background-color: white;
        }
        
        .step-number {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }
        
        .step.active .step-number {
            background-color: white;
            color: var(--primary-color);
        }
        
        .step-label {
            position: absolute;
            white-space: nowrap;
            top: 25px;
            left: 0;
            font-size: 11px;
            opacity: 0.9;
        }
        
        .step:last-child .step-label {
            right: 0;
            left: auto;
            text-align: right;
        }
        
        .toolbar {
            display: flex;
            align-items: center;
            gap: 0.8rem;
        }
        
        .btn-help {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.8rem;
        }
        
        .btn-back {
            display: flex;
            align-items: center;
            gap: 0.4rem;
            background-color: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
        }
        
        /* 主内容区 */
        .main-content {
            flex: 1;
            padding: 0;
            display: flex;
            overflow: hidden;
        }
        
        /* 左侧列：配置参数 */
        .left-column {
            width: 50%;
            padding: 1rem;
            border-right: 1px solid var(--border-color);
            overflow-y: auto;
        }
        
        /* 右侧列：文件选择 */
        .right-column {
            width: 50%;
            padding: 1rem;
            overflow-y: auto;
        }
        
        .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 1rem;
            font-weight: 500;
            color: var(--text-color);
        }
        
        .section-icon {
            margin-right: 0.5rem;
            font-size: 1.1rem;
            color: var(--primary-color);
        }
        
        /* 表单元素 */
        .form-section {
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }
        
        .form-section-title {
            font-weight: 500;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
        
        .option-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .option-item {
            display: flex;
            align-items: flex-start;
            gap: 0.8rem;
            padding: 0.8rem;
            border-radius: 6px;
            transition: all 0.2s;
            border: 1px solid var(--border-color);
            background-color: white;
        }
        
        .option-item:hover {
            background-color: rgba(59, 130, 246, 0.05);
        }
        
        .option-item.selected {
            background-color: rgba(59, 130, 246, 0.1);
            border: 1px solid var(--primary-light);
        }
        
        .radio-input, .checkbox-input {
            margin-top: 0.2rem;
        }
        
        .option-content {
            flex-grow: 1;
        }
        
        .option-label {
            font-weight: 500;
            font-size: 0.9rem;
            margin-bottom: 0.2rem;
        }
        
        .option-desc {
            font-size: 0.8rem;
            color: var(--text-light);
        }
        
        /* 文件选择区域 */
        .file-drop-area {
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 2rem 1rem;
            text-align: center;
            background-color: rgba(59, 130, 246, 0.03);
            margin-bottom: 1.5rem;
            transition: all 0.2s;
        }
        
        .file-drop-area:hover {
            border-color: var(--primary-light);
            background-color: rgba(59, 130, 246, 0.05);
        }
        
        .file-icon {
            font-size: 2.5rem;
            color: var(--primary-light);
            margin-bottom: 1rem;
        }
        
        .file-text {
            font-weight: 500;
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
        }
        
        .file-subtext {
            color: var(--text-light);
            font-size: 0.8rem;
            margin-bottom: 1rem;
        }
        
        .files-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.85rem;
        }
        
        .files-table th,
        .files-table td {
            padding: 0.6rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .files-table th {
            font-weight: 500;
            color: var(--text-light);
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 0.85rem;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #153070;
        }
        
        .btn-outline {
            background-color: transparent;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }
        
        .btn-outline:hover {
            background-color: rgba(30, 58, 138, 0.05);
        }
        
        .btn-sm {
            padding: 0.3rem 0.6rem;
            font-size: 0.75rem;
        }
        
        /* 底部工具栏 */
        .bottom-toolbar {
            background-color: white;
            padding: 0.8rem 1rem;
            display: flex;
            justify-content: space-between;
            border-top: 1px solid var(--border-color);
        }
        
        .toolbar-left {
            display: flex;
            gap: 0.8rem;
        }
        
        .toolbar-right {
            display: flex;
            gap: 0.8rem;
        }
        
        .btn-next {
            background-color: var(--accent-color);
            color: white;
        }
        
        .btn-next:hover {
            background-color: #0284C7;
        }
        
        /* 帮助和日志模态框 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
        }

        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .modal {
            background-color: white;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            transform: translateY(-20px);
            transition: all 0.3s;
        }

        .modal-overlay.active .modal {
            transform: translateY(0);
        }

        .modal-header {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-weight: 600;
            font-size: 1.1rem;
        }

        .modal-close {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1.2rem;
            color: var(--text-light);
        }

        .modal-body {
            padding: 1rem;
        }

        .help-section {
            margin-bottom: 1rem;
        }

        .help-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }
        
        /* 执行操作页面的卡片样式 */
        .stats-container {
            margin-top: 2rem;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }
        
        .stat-card {
            background-color: white;
            border-radius: 8px;
            padding: 1.2rem;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }
        
        .stat-title {
            font-size: 0.85rem;
            font-weight: 500;
            color: var(--text-light);
            margin-bottom: 0.3rem;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .stat-desc {
            font-size: 0.8rem;
            color: var(--text-light);
        }
        
        .stat-icon {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            position: absolute;
            right: 1rem;
            top: 1rem;
        }
        
        .icon-blue {
            background-color: rgba(59, 130, 246, 0.1);
            color: var(--primary-light);
        }
        
        .icon-green {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }
        
        .icon-amber {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }
        
        .icon-red {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        /* 进度条样式 */
        .progress-panel {
            background-color: white;
            border-radius: 8px;
            padding: 1.2rem;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            margin-bottom: 1.5rem;
            width: 100%;
        }
        
        .progress-panel-title {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 1rem;
            font-weight: 500;
            color: var(--text-color);
        }
        
        .progress-container {
            margin-bottom: 1rem;
        }
        
        .progress-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .progress-label {
            font-weight: 500;
            font-size: 0.9rem;
        }
        
        .progress-value {
            font-weight: 600;
            font-size: 0.9rem;
            color: var(--primary-color);
        }
        
        .progress-bar {
            height: 8px;
            background-color: rgba(59, 130, 246, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background-color: var(--primary-light);
            border-radius: 4px;
            width: 28%;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 顶部区域 -->
        <header class="header">
            <div class="logo-area">
                <div class="logo">BOM</div>
                <div>
                    <div class="title">BOM表制作工具</div>
                    <div class="subtitle">天锻重工数据处理系统</div>
                </div>
            </div>
            
            <div class="steps-indicator">
                <div class="step active">
                    <div class="step-number">1</div>
                    <div class="step-label">配置与文件选择</div>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-label">执行操作</div>
                </div>
            </div>
            
            <div class="toolbar">
                <button class="btn-help">?</button>
                <button class="btn-back">
                    返回主菜单 ↩
                </button>
            </div>
        </header>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 左侧列：配置参数 -->
            <div class="left-column">
                <div class="section-title">
                    <span class="section-icon">⚙️</span>
                    <span>配置参数</span>
                </div>
                
                <!-- 第一个图片的进度条内容 -->
                <div class="progress-panel">
                    <div class="progress-panel-title">
                        <span class="section-icon">⚙️</span>
                        <span>数据处理状态</span>
                    </div>
                    
                    <div class="progress-container">
                        <div class="progress-header">
                            <span class="progress-label">总体进度</span>
                            <span class="progress-value">28%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                    </div>
                    
                    <div class="progress-container">
                        <div class="progress-header">
                            <span class="progress-label">当前任务: 正在读取源文件</span>
                            <span class="progress-value">345/1250</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 28%"></div>
                        </div>
                    </div>
                </div>
                
                <div class="form-section">
                    <div class="form-section-title">特殊外购件搜索模式:</div>
                    <div class="option-group">
                        <div class="option-item selected">
                            <input type="radio" name="searchMode" id="searchMode1" class="radio-input" checked>
                            <div class="option-content">
                                <label for="searchMode1" class="option-label">模式1: 模糊搜索 (组合B、C、D列)</label>
                                <div class="option-desc">将B、C、D列的值组合起来进行模糊匹配</div>
                            </div>
                        </div>
                        <div class="option-item">
                            <input type="radio" name="searchMode" id="searchMode2" class="radio-input">
                            <div class="option-content">
                                <label for="searchMode2" class="option-label">模式2: 精确搜索 (B、C、D列作为关键词)</label>
                                <div class="option-desc">使用B、C、D列的值作为精确关键词匹配</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-section">
                    <div class="form-section-title">操作模式:</div>
                    <div class="option-group">
                        <div class="option-item selected">
                            <input type="radio" name="operationMode" id="operationMode1" class="radio-input" checked>
                            <div class="option-content">
                                <label for="operationMode1" class="option-label">第一步: A→B映射</label>
                                <div class="option-desc">生成自制件和工作簿B的工作表D的数据</div>
                            </div>
                        </div>
                        <div class="option-item">
                            <input type="radio" name="operationMode" id="operationMode2" class="radio-input">
                            <div class="option-content">
                                <label for="operationMode2" class="option-label">第二步: B→C映射</label>
                                <div class="option-desc">将工作簿B的数据映射到工作簿C</div>
                            </div>
                        </div>
                        <div class="option-item">
                            <input type="radio" name="operationMode" id="operationMode3" class="radio-input">
                            <div class="option-content">
                                <label for="operationMode3" class="option-label">执行全部映射</label>
                                <div class="option-desc">一次性完成所有映射操作</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-section">
                    <div class="form-section-title">其他选项:</div>
                    <div class="option-group">
                        <div class="option-item selected">
                            <input type="checkbox" id="showPreview" class="checkbox-input" checked>
                            <div class="option-content">
                                <label for="showPreview" class="option-label">完成后显示预览</label>
                            </div>
                        </div>
                        <div class="option-item selected">
                            <input type="checkbox" id="saveLog" class="checkbox-input" checked>
                            <div class="option-content">
                                <label for="saveLog" class="option-label">保存日志文件</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 处理统计卡片 -->
                <div class="stats-container">
                    <div class="stat-card">
                        <div class="stat-title">已处理记录</div>
                        <div class="stat-value">1229</div>
                        <div class="stat-desc">总记录数: 1250</div>
                        <div class="stat-icon icon-blue">📊</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-title">成功匹配</div>
                        <div class="stat-value">1056</div>
                        <div class="stat-desc">匹配率: 86%</div>
                        <div class="stat-icon icon-green">✓</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-title">警告记录</div>
                        <div class="stat-value">46</div>
                        <div class="stat-desc">需要人工确认</div>
                        <div class="stat-icon icon-amber">⚠️</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-title">错误记录</div>
                        <div class="stat-value">127</div>
                        <div class="stat-desc">处理失败需要修复</div>
                        <div class="stat-icon icon-red">❌</div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧列：文件选择 -->
            <div class="right-column">
                <div class="section-title">
                    <span class="section-icon">📂</span>
                    <span>文件选择</span>
                </div>
                
                <div class="file-drop-area">
                    <div class="file-icon">📄</div>
                    <div class="file-text">拖放文件到此处或点击选择</div>
                    <div class="file-subtext">支持 Excel 文件 (.xlsx, .xls)</div>
                    <button class="btn btn-primary">选择文件</button>
                </div>
                
                <div class="form-section-title">已选择文件</div>
                <table class="files-table">
                    <thead>
                        <tr>
                            <th>文件类型</th>
                            <th>文件路径</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>源数据文件 (A)</td>
                            <td>未选择</td>
                            <td><button class="btn btn-outline btn-sm">选择</button></td>
                        </tr>
                        <tr>
                            <td>目标文件 (B)</td>
                            <td>未选择</td>
                            <td><button class="btn btn-outline btn-sm">选择</button></td>
                        </tr>
                        <tr>
                            <td>第三个文件 (C)</td>
                            <td>未选择</td>
                            <td><button class="btn btn-outline btn-sm">选择</button></td>
                        </tr>
                        <tr>
                            <td>外购件库路径</td>
                            <td>purchased_parts_db</td>
                            <td><button class="btn btn-outline btn-sm">选择</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </main>
        
        <!-- 底部工具栏 -->
        <div class="bottom-toolbar">
            <div class="toolbar-left">
                <button class="btn btn-outline" id="btnViewLog">
                    <span>📋</span> 查看处理日志
                </button>
            </div>
            <div class="toolbar-right">
                <button class="btn btn-outline">取消</button>
                <button class="btn btn-primary">保存配置</button>
                <button class="btn btn-next">执行映射操作</button>
            </div>
        </div>
    </div>
    
    <!-- 帮助模态框 -->
    <div class="modal-overlay" id="helpModal">
        <div class="modal">
            <div class="modal-header">
                <div class="modal-title">帮助信息</div>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="help-section">
                    <div class="help-title">配置参数</div>
                    <p>在此区域设置数据处理的各种参数，包括搜索模式、操作模式等。选择合适的参数可以提高数据处理的准确性和效率。</p>
                </div>
                <div class="help-section">
                    <div class="help-title">文件选择</div>
                    <p>您需要选择以下文件：</p>
                    <ul>
                        <li><strong>源数据文件 (A)</strong>: 包含原始数据的Excel文件</li>
                        <li><strong>目标文件 (B)</strong>: 将接收处理后数据的Excel文件</li>
                        <li><strong>第三个文件 (C)</strong>: 用于第二步映射的Excel文件</li>
                        <li><strong>外购件库路径</strong>: 包含外购件信息的数据库文件夹</li>
                    </ul>
                </div>
                <div class="help-section">
                    <div class="help-title">操作流程</div>
                    <p>1. 选择适当的配置参数和文件</p>
                    <p>2. 点击"执行映射操作"按钮开始处理</p>
                    <p>3. 处理完成后可以查看日志了解详细信息</p>
                </div>
                <div class="help-section">
                    <div class="help-title">处理统计</div>
                    <p>处理完成后，可以查看各类统计数据：</p>
                    <ul>
                        <li><strong>已处理记录</strong>: 已完成处理的记录总数</li>
                        <li><strong>成功匹配</strong>: 成功找到匹配项并完成映射的记录</li>
                        <li><strong>警告记录</strong>: 处理过程中发现的需要注意的记录</li>
                        <li><strong>错误记录</strong>: 处理失败需要修复的记录</li>
                    </ul>
                </div>
                <div class="help-section">
                    <div class="help-title">数据处理状态</div>
                    <p>在处理过程中，您可以实时查看：</p>
                    <ul>
                        <li><strong>总体进度</strong>: 显示整个处理过程的完成百分比</li>
                        <li><strong>当前任务</strong>: 显示正在执行的任务及其进度</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 日志模态框 -->
    <div class="modal-overlay" id="logModal">
        <div class="modal">
            <div class="modal-header">
                <div class="modal-title">处理日志</div>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="log-entry success">
                    <div class="log-timestamp">2023-06-15 10:23:45</div>
                    <div class="log-message">成功加载源数据文件，共读取1250条记录</div>
                </div>
                <div class="log-entry success">
                    <div class="log-timestamp">2023-06-15 10:23:47</div>
                    <div class="log-message">成功加载目标文件模板</div>
                </div>
                <div class="log-entry warning">
                    <div class="log-timestamp">2023-06-15 10:23:50</div>
                    <div class="log-message">发现46条无法匹配的记录，将使用默认值处理</div>
                </div>
                <div class="log-entry success">
                    <div class="log-timestamp">2023-06-15 10:24:05</div>
                    <div class="log-message">已处理345条记录，进度28%</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 单选按钮组逻辑
        document.querySelectorAll('input[type="radio"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // 获取所有同名单选按钮的父元素
                const name = this.getAttribute('name');
                document.querySelectorAll(`input[name="${name}"]`).forEach(input => {
                    const parent = input.closest('.option-item');
                    if (input.checked) {
                        parent.classList.add('selected');
                    } else {
                        parent.classList.remove('selected');
                    }
                });
            });
        });
        
        // 复选框逻辑
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const parent = this.closest('.option-item');
                if (this.checked) {
                    parent.classList.add('selected');
                } else {
                    parent.classList.remove('selected');
                }
            });
        });
        
        // 点击选项项目时也触发选择
        document.querySelectorAll('.option-item').forEach(item => {
            item.addEventListener('click', function(e) {
                // 如果点击的是input本身，则不执行下面的代码，避免双重触发
                if (e.target.tagName === 'INPUT') return;
                
                const input = this.querySelector('input');
                if (input.type === 'radio') {
                    input.checked = true;
                    // 手动触发change事件
                    input.dispatchEvent(new Event('change'));
                } else if (input.type === 'checkbox') {
                    input.checked = !input.checked;
                    // 手动触发change事件
                    input.dispatchEvent(new Event('change'));
                }
            });
        });
        
        // 帮助按钮点击事件
        document.querySelector('.btn-help').addEventListener('click', function() {
            document.getElementById('helpModal').classList.add('active');
        });
        
        // 查看日志按钮点击事件
        document.querySelector('#btnViewLog').addEventListener('click', function() {
            document.getElementById('logModal').classList.add('active');
        });
        
        // 关闭模态框按钮点击事件
        document.querySelectorAll('.modal-close').forEach(button => {
            button.addEventListener('click', function() {
                this.closest('.modal-overlay').classList.remove('active');
            });
        });
        
        // 点击模态框外部关闭模态框
        document.querySelectorAll('.modal-overlay').forEach(overlay => {
            overlay.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.classList.remove('active');
                }
            });
        });
        
        // 执行映射操作按钮点击事件
        document.querySelector('.btn-next').addEventListener('click', function() {
            alert('点击此按钮将直接执行映射操作，进入执行操作页面。');
        });
        
        // 模拟进度条动画
        let progress = 0;
        const interval = setInterval(() => {
            if (progress < 28) {
                progress += 1;
                document.querySelector('.progress-fill').style.width = progress + '%';
            } else {
                clearInterval(interval);
            }
        }, 50);
    </script>
</body>
</html>