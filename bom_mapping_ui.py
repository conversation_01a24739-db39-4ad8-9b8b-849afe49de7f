import os
import sys
import tkinter as tk
from tkinter import filedialog, messagebox, PhotoImage
from PIL import Image, ImageTk
import pandas as pd
from pathlib import Path
import datetime
import json
import subprocess
import math
from threading import Thread
import customtkinter as ctk
import pythoncom
import glob

# 尝试导入bom_mapping模块
bom_mapping = None
try:
    import bom_mapping
except ImportError as e:
    print(f"导入bom_mapping模块失败: {str(e)}")

class ModernBomMappingUI(ctk.CTkFrame):
    def __init__(self, master, return_callback=None, **kwargs):
        super().__init__(master, **kwargs)
        self.master = master
        self.is_processing = False
        self.return_callback = return_callback
        self.log_history = []
        self.log_viewer_window = None
        self.overlay = None

        # --- Color Palette ---
        self.COLOR_PRIMARY = "#1E3A8A"
        self.COLOR_PRIMARY_LIGHT = "#3B82F6"
        self.COLOR_ACCENT = "#0EA5E9"
        self.COLOR_BG = "#F8FAFC"
        self.COLOR_CARD_BG = "#FFFFFF"
        self.COLOR_TEXT = "#1E293B"
        self.COLOR_TEXT_LIGHT = "#64748B"
        self.COLOR_BORDER = "#E2E8F0"
        self.COLOR_SUCCESS = "#10B981"
        self.COLOR_WARNING = "#F59E0B"
        self.COLOR_ERROR = "#EF4444"
        self.COLOR_TEXT_TIMESTAMP = "#94A3B8"

        # 新增: 日志条目专用颜色
        self.LOG_BG_SUCCESS = "#F0FDF4"
        self.LOG_TEXT_SUCCESS = "#15803D"
        self.LOG_BG_ERROR = "#FEF2F2"
        self.LOG_TEXT_ERROR = "#B91C1C"
        self.LOG_BG_WARNING = "#FFFBEB"
        self.LOG_TEXT_WARNING = "#B45309"
        self.LOG_BG_INFO = "#F8FAFC"
        self.LOG_TEXT_INFO = "#475569"

        # 新增: 统计卡片颜色
        self.STATS_ICON_BG_PROCESSED = "#E0F2FE"
        self.STATS_ICON_TEXT_PROCESSED = "#0284C7"
        self.STATS_ICON_BG_SUCCESS = "#F0FDF4"
        self.STATS_ICON_TEXT_SUCCESS = "#15803D"
        self.STATS_ICON_BG_WARNING = "#FFFBEB"
        self.STATS_ICON_TEXT_WARNING = "#B45309"
        self.STATS_ICON_BG_ERROR = "#FEF2F2"
        self.STATS_ICON_TEXT_ERROR = "#B91C1C"

        # 新增: 为选项高亮提供颜色
        self.COLOR_OPTION_SELECTED_BG = "#EFF6FF"
        self.COLOR_OPTION_SELECTED_BORDER = "#60A5FA"

        self.pack(fill="both", expand=True)
        
        # --- Initialize Variables ---
        self.initialize_variables()

        if isinstance(master, ctk.CTk) or isinstance(master, tk.Toplevel):
             self.master.title("BOM表制作工具")
             # Set window to 80% of the screen and center it for better usability
             screen_width = self.master.winfo_screenwidth()
             screen_height = self.master.winfo_screenheight()
             width = int(screen_width * 0.8)
             height = int(screen_height * 0.8)
             x = (screen_width - width) // 2
             y = (screen_height - height) // 3
             self.master.geometry(f"{width}x{height}+{x}+{y}")
             self.master.minsize(1100, 750)

        ctk.set_appearance_mode("Light")
        
        # --- Create UI ---
        self.create_header()
        self.create_main_content()
        self.create_bottom_toolbar()

        # --- Final Setup ---
        self.load_last_paths()
        
        if bom_mapping is None:
            messagebox.showerror("模块错误", "无法导入bom_mapping模块，核心功能将无法正常工作。")

    def initialize_variables(self):
        """初始化变量"""
        self.source_file = tk.StringVar()
        self.target_file = tk.StringVar()
        self.third_file = tk.StringVar()
        self.purchased_parts_dir = tk.StringVar()

        self.file_display_vars = {
            "source_file": tk.StringVar(value="未选择文件"),
            "target_file": tk.StringVar(value="未选择文件"),
            "third_file": tk.StringVar(value="未选择文件"),
            "purchased_parts_dir": tk.StringVar(value=""),
        }
        
        self.source_sheet = tk.StringVar()
        self.target_sheet = tk.StringVar()
        self.third_sheet = tk.StringVar()
        
        self.source_sheet_options = ['-']
        self.target_sheet_options = ['-']
        self.third_sheet_options = ['-']

        self.target_start_row = tk.IntVar(value=5)
        self.search_mode = tk.StringVar(value="2")
        self.operation_mode = tk.StringVar(value="3")
        
        self.default_purchased_parts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "purchased_parts_db")
        if not os.path.exists(self.default_purchased_parts_dir):
            os.makedirs(self.default_purchased_parts_dir)
        self.purchased_parts_dir.set(self.default_purchased_parts_dir)
        self.file_display_vars["purchased_parts_dir"].set(os.path.basename(os.path.normpath(self.default_purchased_parts_dir)))

        self.paths_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "saved_paths.json")
        self.is_processing = False
        self.not_found_parts_message = ""
        
        # 新增: 统计数据变量 (使用中性默认值)
        self.stats_processed = tk.StringVar(value="0")
        self.stats_total = tk.StringVar(value="总记录数: 0")
        self.stats_success = tk.StringVar(value="0")
        self.stats_match_rate = tk.StringVar(value="匹配率: --%")
        self.stats_warning = tk.StringVar(value="0")
        self.stats_warning_desc = tk.StringVar(value="需要人工确认")
        self.stats_error = tk.StringVar(value="0")
        self.stats_error_desc = tk.StringVar(value="处理失败需要修复")
        
    def create_header(self):
        header = ctk.CTkFrame(self, fg_color=self.COLOR_PRIMARY, corner_radius=0, height=60)
        header.pack(fill="x", side="top")
        header.grid_columnconfigure(1, weight=1)

        logo_area = ctk.CTkFrame(header, fg_color="transparent")
        logo_area.grid(row=0, column=0, padx=20, pady=10, sticky="w")
        
        logo_icon = ctk.CTkLabel(logo_area, text="BOM", fg_color="white", text_color=self.COLOR_PRIMARY, width=32, height=32, font=("Microsoft YaHei UI", 10, "bold"), corner_radius=6)
        logo_icon.pack(side="left", padx=(0, 12))

        title_frame = ctk.CTkFrame(logo_area, fg_color="transparent")
        title_frame.pack(side="left")
        ctk.CTkLabel(title_frame, text="BOM表制作工具", text_color="white", font=("Microsoft YaHei UI", 16, "bold")).pack(anchor="w")
        ctk.CTkLabel(title_frame, text="天锻重工数据处理系统", text_color="#D1D5DB", font=("Microsoft YaHei UI", 10)).pack(anchor="w")
        
        toolbar = ctk.CTkFrame(header, fg_color="transparent")
        toolbar.grid(row=0, column=2, padx=20, pady=10, sticky="e")
        ctk.CTkButton(toolbar, text="?", width=28, height=28, corner_radius=14, fg_color="#4B61A1", hover_color="#6275AD", text_color="white", font=("Microsoft YaHei UI", 12, "bold"), command=self.show_help).pack(side="left", padx=5)
        ctk.CTkButton(toolbar, text="返回主菜单 ↩", command=self.return_to_main, fg_color="#4B61A1", hover_color="#6275AD", text_color="white", font=("Microsoft YaHei UI", 12)).pack(side="left", padx=5)

    def create_main_content(self):
        main_content = ctk.CTkFrame(self, fg_color="transparent")
        main_content.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        main_content.grid_columnconfigure(0, weight=4)
        main_content.grid_columnconfigure(1, weight=6)
        main_content.grid_rowconfigure(0, weight=1)

        left_column = ctk.CTkScrollableFrame(main_content, fg_color=self.COLOR_BG, corner_radius=6)
        left_column.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        
        right_column = ctk.CTkScrollableFrame(main_content, fg_color=self.COLOR_BG, corner_radius=6)
        right_column.grid(row=0, column=1, sticky="nsew", padx=(5, 0))

        self.create_left_column_widgets(left_column)
        self.create_right_column_widgets(right_column)
    
    def create_titled_frame(self, parent, title, icon_path_or_text):
        frame = ctk.CTkFrame(parent, fg_color="transparent")
        frame.pack(fill="x", pady=(15, 5), padx=10)
        
        icon_image = None
        if icon_path_or_text.endswith(('.png', '.jpg', '.ico')):
             try:
                icon_full_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), icon_path_or_text)
                icon_image = ctk.CTkImage(Image.open(icon_full_path), size=(20, 20))
             except Exception as e:
                print(f"无法加载标题图标 '{icon_path_or_text}': {e}")
                icon_path_or_text = "📁" # Fallback to emoji
        
        # 为图标和文本创建独立的标签以控制间距
        if icon_image:
            icon_label = ctk.CTkLabel(frame, image=icon_image, text="")
            icon_label.pack(side="left", anchor="w")
            title_label = ctk.CTkLabel(frame, text=title, text_color=self.COLOR_TEXT, font=("Microsoft YaHei UI", 16, "bold"))
            title_label.pack(side="left", anchor="w", padx=(8, 0))
        else: # 对于文本图标，使用f-string添加间距
            title_label = ctk.CTkLabel(frame, text=f"{icon_path_or_text}  {title}", text_color=self.COLOR_TEXT, font=("Microsoft YaHei UI", 16, "bold"))
            title_label.pack(anchor="w")
    
    def create_left_column_widgets(self, parent):
        # 使用图片路径代替Emoji
        self.create_titled_frame(parent, "文件选择与配置", "icons/文件夹.png")
        self.create_files_table(parent)
        self.create_titled_frame(parent, "实时统计", "icons/数据.png")
        self.create_stats_display(parent)

    def create_right_column_widgets(self, parent):
        self.create_titled_frame(parent, "处理选项与控制", "icons/设置.png")
        
        options_container = ctk.CTkFrame(parent, fg_color="transparent")
        options_container.pack(fill='x', expand=False, padx=10)
        self.create_settings_options(options_container)

    def create_settings_options(self, parent):
        settings_frame = ctk.CTkFrame(parent, fg_color=self.COLOR_CARD_BG, border_color=self.COLOR_BORDER, border_width=1, corner_radius=8)
        settings_frame.pack(fill='x', pady=5)
        
        # 调整了选项组的顺序和文本以匹配截图，并实现了新的UI样式
        self.create_options_group(settings_frame, "特殊外购件搜索模式:", [
            ("模式1: 模糊搜索 (组合B、C、D列)", "将B、C、D列的值组合起来进行模糊匹配", "1"),
            ("模式2: 精确搜索 (B、C、D列作为关键词)", "使用B、C、D列的值作为精确关键词匹配", "2")
        ], self.search_mode)
        
        self.create_options_group(settings_frame, "操作模式:", [
            ("第一步: A→B映射", "生成自制件和工作簿B的工作表D的数据", "1"),
            ("第二步: B→C映射", "将工作簿B的数据映射到工作簿C", "2"),
            ("执行全部映射", "一次性完成所有映射操作", "3")
        ], self.operation_mode)

    def create_bottom_toolbar(self):
        bottom_bar = ctk.CTkFrame(self, fg_color=self.COLOR_CARD_BG, corner_radius=0, height=70, border_color=self.COLOR_BORDER, border_width=1)
        bottom_bar.pack(fill="x", side="bottom", pady=(5,0))
        # 重新配置网格以容纳左侧的日志按钮
        bottom_bar.grid_columnconfigure(0, weight=0)
        bottom_bar.grid_columnconfigure(1, weight=1)
        bottom_bar.grid_columnconfigure(2, weight=0)

        # 左侧按钮（日志）
        left_button_frame = ctk.CTkFrame(bottom_bar, fg_color="transparent")
        left_button_frame.grid(row=0, column=0, rowspan=2, sticky='w', padx=20, pady=10)
        
        # 加载图标
        try:
            log_icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "icons", "处理日志.png")
            log_icon_image = ctk.CTkImage(Image.open(log_icon_path), size=(20, 20))
        except Exception as e:
            print(f"无法加载日志图标: {e}")
            log_icon_image = None
            
        ctk.CTkButton(left_button_frame, text="处理日志", image=log_icon_image, compound="left", font=("Microsoft YaHei UI", 12), fg_color="transparent", border_color=self.COLOR_BORDER, border_width=1, text_color=self.COLOR_TEXT, hover_color="#F1F5F9", command=self.show_log_viewer).pack()

        # 中间进度条
        progress_frame = ctk.CTkFrame(bottom_bar, fg_color='transparent')
        progress_frame.grid(row=0, column=1, rowspan=2, sticky='ew', padx=20, pady=5)
        progress_frame.grid_columnconfigure(0, weight=1)
        
        self.overall_progress_bar = ctk.CTkProgressBar(progress_frame, progress_color=self.COLOR_PRIMARY_LIGHT, fg_color=self.COLOR_BORDER)
        self.overall_progress_bar.set(0)
        self.overall_progress_bar.grid(row=0, column=0, sticky='ew')
        
        self.task_progress_text = ctk.CTkLabel(progress_frame, text="等待操作...", font=("Microsoft YaHei UI", 11), text_color=self.COLOR_TEXT_LIGHT)
        self.task_progress_text.grid(row=1, column=0, sticky='w', pady=(2,0))
        
        # 右侧按钮（主要操作）
        button_frame = ctk.CTkFrame(bottom_bar, fg_color="transparent")
        button_frame.grid(row=0, column=2, rowspan=2, sticky='e', padx=20, pady=10)

        ctk.CTkButton(button_frame, text="历史记录", font=("Microsoft YaHei UI", 12), fg_color="transparent", border_color=self.COLOR_BORDER, border_width=1, text_color=self.COLOR_TEXT_LIGHT, hover_color="#F1F5F9", command=self.show_history).pack(side="left", padx=5)
        ctk.CTkButton(button_frame, text="保存未匹配", font=("Microsoft YaHei UI", 12), fg_color="transparent", border_color=self.COLOR_BORDER, border_width=1, text_color=self.COLOR_TEXT_LIGHT, hover_color="#F1F5F9", command=self.save_not_found_parts_file).pack(side="left", padx=5)
        self.execute_button = ctk.CTkButton(button_frame, text="执行映射操作", font=("Microsoft YaHei UI", 13, "bold"), fg_color=self.COLOR_ACCENT, hover_color="#0284C7", command=self.process_mapping, height=36)
        self.execute_button.pack(side="left", padx=10)

    def create_files_table(self, parent):
        # 将此框架设为透明，使其继承父级的背景色，仅用于组织和添加边距
        files_frame = ctk.CTkFrame(parent, fg_color="transparent")
        files_frame.pack(fill="x", pady=5, padx=10)

        file_data = [
            {"label": "源文件 (A)", "key": "source_file"},
            {"label": "目标文件 (B)", "key": "target_file"},
            {"label": "第三文件 (C)", "key": "third_file"},
            {"label": "外购件库", "key": "purchased_parts_dir"},
        ]
        
        for item in file_data:
            self.create_file_entry(files_frame, item)
    
    def create_file_entry(self, parent, item_data):
        key = item_data["key"]
        # 每个文件条目都放在一个白色、带边框的卡片中，以在灰色背景上突出显示
        entry_frame = ctk.CTkFrame(parent, fg_color=self.COLOR_CARD_BG, corner_radius=8, border_width=1, border_color=self.COLOR_BORDER)
        entry_frame.pack(fill="x", padx=0, pady=4)
        entry_frame.grid_columnconfigure(0, weight=1)

        # 标题
        title_label = ctk.CTkLabel(entry_frame, text=item_data["label"], font=("Microsoft YaHei UI", 13, "bold"), fg_color="transparent")
        title_label.grid(row=0, column=0, sticky="w", padx=15, pady=(10, 5))
        
        # 路径和按钮的容器
        path_frame = ctk.CTkFrame(entry_frame, fg_color="transparent")
        path_frame.grid(row=1, column=0, sticky='ew', padx=15, pady=(0, 10))
        path_frame.grid_columnconfigure(0, weight=1)

        # 路径输入框的背景色设为浅灰，以和白色卡片区分
        path_entry = ctk.CTkEntry(path_frame, textvariable=self.file_display_vars[key], fg_color=self.COLOR_BG, border_width=0, state="readonly", corner_radius=6)
        path_entry.grid(row=0, column=0, sticky='ew', padx=(0, 10))
        path_entry.bind("<Button-1>", lambda e, k=key: self.open_file_or_folder(getattr(self, k).get()))
        
        # 浏览按钮
        browse_button = ctk.CTkButton(path_frame, text="浏览...", width=80, command=lambda k=key: self.select_path(k))
        browse_button.grid(row=0, column=1, sticky='e')

        # --- 动态高亮效果 ---
        def on_enter(event):
            entry_frame.configure(border_color=self.COLOR_PRIMARY_LIGHT, border_width=2)

        def on_leave(event):
            entry_frame.configure(border_color=self.COLOR_BORDER, border_width=1)

        # 将悬停事件绑定到卡片及其内部的主要组件上，以提供无缝的高亮体验
        widgets_to_bind = [entry_frame, path_frame, path_entry, title_label]
        for widget in widgets_to_bind:
            widget.bind("<Enter>", on_enter)
            widget.bind("<Leave>", on_leave)
            
    def on_sheet_select(self, choice):
        self.save_last_paths()

    def create_options_group(self, parent, title, options, variable):
        """创建带高亮效果的单选按钮组，使用嵌套框架模拟边框以解决渲染问题"""
        group_frame = ctk.CTkFrame(parent, fg_color="transparent")
        group_frame.pack(fill="x", pady=10, padx=15)
        ctk.CTkLabel(group_frame, text=title, font=("Microsoft YaHei UI", 13, "bold")).pack(anchor="w", pady=(0, 8))
        
        option_widgets = []

        def _update_styles():
            current_value = variable.get()
            for border_card, content_card, v in option_widgets:
                if v == current_value:
                    # 选中状态：外框为高亮边框色，内框为高亮背景色
                    border_card.configure(fg_color=self.COLOR_OPTION_SELECTED_BORDER)
                    content_card.configure(fg_color=self.COLOR_OPTION_SELECTED_BG)
                else:
                    # 未选中状态：外框为普通边框色，内框为普通背景色
                    border_card.configure(fg_color=self.COLOR_BORDER)
                    content_card.configure(fg_color=self.COLOR_CARD_BG)

        def _select_option(value):
            variable.set(value)
            _update_styles()

        def _make_selectable(widget, value):
            # 绑定鼠标点击事件
            widget.bind("<Button-1>", lambda e: _select_option(value))
            # 递归为所有子控件绑定事件，确保点击任何地方都有效
            for child in widget.winfo_children():
                # RadioButton自身有命令，不需要覆盖
                if not isinstance(child, ctk.CTkRadioButton):
                    _make_selectable(child, value)

        for label, desc, value in options:
            # 外层卡片，作为边框
            border_card = ctk.CTkFrame(group_frame, fg_color=self.COLOR_BORDER, corner_radius=8)
            border_card.pack(fill="x", pady=4)

            # 内层卡片，作为内容背景
            content_card = ctk.CTkFrame(border_card, fg_color=self.COLOR_CARD_BG, corner_radius=6)
            content_card.pack(fill="both", expand=True, padx=2, pady=2) # 2px padding creates the border effect
            content_card.grid_columnconfigure(1, weight=1)
            
            # 单选按钮本身，不显示文字，只作为状态控制器
            radio_button = ctk.CTkRadioButton(content_card, text="", variable=variable, value=value, command=_update_styles, width=0)
            radio_button.grid(row=0, column=0, rowspan=2, sticky="ns", padx=(10, 5))
            
            # 选项标题
            title_label = ctk.CTkLabel(content_card, text=label, font=("Microsoft YaHei UI", 12, "bold"), text_color=self.COLOR_TEXT, anchor="w")
            title_label.grid(row=0, column=1, sticky="ew", pady=(8, 0), padx=5)

            # 选项描述
            desc_label = ctk.CTkLabel(content_card, text=desc, font=("Microsoft YaHei UI", 11), text_color=self.COLOR_TEXT_LIGHT, anchor="w", wraplength=400, justify="left")
            desc_label.grid(row=1, column=1, sticky="ew", pady=(0, 8), padx=5)
            
            # 让整个卡片都可点击
            _make_selectable(content_card, value)
            
            option_widgets.append((border_card, content_card, value))

        # 初始加载时更新一次样式
        self.master.after(100, _update_styles)

    def select_path(self, key):
        is_dir = "dir" in key
        if is_dir: path = filedialog.askdirectory(title=f"选择 - {key}")
        else: path = filedialog.askopenfilename(title=f"选择 - {key}", filetypes=[("Excel Files", "*.xlsx *.xls")])
        
        if path:
            getattr(self, key).set(path)
            self.file_display_vars[key].set(os.path.basename(path))

            if not is_dir:
                if key == "source_file": 
                    self.set_source_worksheet(path)
                    self.update_total_records(path) # 触发总数计算
                elif key == "target_file": self.set_target_worksheet(path)
                elif key == "third_file": self.set_third_worksheet(path)

            self.log(f"已选择 {key}: {path}", "INFO")
            self.save_last_paths()

    def update_progress(self, percentage, status_text=None):
        if not self.winfo_exists(): return
        def _update():
            if not self.winfo_exists(): return
            self.overall_progress_bar.set(percentage / 100.0)
            if status_text: self.task_progress_text.configure(text=status_text)
        self.master.after(0, _update)

    def log(self, message, level="INFO", title=None):
        """
        将日志消息安全地记录到历史记录中，并调度UI更新。
        此方法是线程安全的，可以从任何线程调用。
        """
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = (timestamp, message, level.upper())
        self.log_history.append(log_entry)

        def _update_gui():
            # 确保UI更新在主线程中执行
            if self.log_viewer_window and self.log_viewer_window.winfo_exists():
                if hasattr(self, 'log_textbox') and self.log_textbox.winfo_exists():
                    self.log_textbox.configure(state="normal")
                    self.log_textbox.insert("end", f"{timestamp}\n", "TIMESTAMP")
                    self.log_textbox.insert("end", f"    {message}\n\n", level.upper())
                    self.log_textbox.configure(state="disabled")
                    self.log_textbox.see("end")
        
        if self.winfo_exists():
            self.master.after(0, _update_gui)

    def process_mapping(self):
        if self.is_processing:
            messagebox.showwarning("处理中", "当前有任务正在执行，请稍候。")
            return

        op_mode = self.operation_mode.get()
        if not self.source_file.get() or not self.target_file.get() or ((op_mode == "2" or op_mode == "3") and not self.third_file.get()):
            messagebox.showerror("错误", "请确保已选择所有必需的文件。")
            return
                
        self.is_processing = True
        self.execute_button.configure(state="disabled", text="处理中...")
        self.log("开始执行映射操作...", "INFO")
        self.update_progress(0, "正在准备...")
        Thread(target=self.run_mapping_logic, daemon=True).start()

    def run_mapping_logic(self):
        pythoncom.CoInitialize()
        try:
            def enhanced_log_with_progress(message):
                level = "INFO"
                if "错误" in message or "失败" in message: level = "ERROR"
                elif "警告" in message: level = "WARNING"
                elif "成功" in message or "完成" in message: level = "SUCCESS"
                self.log(message, level)

                if "处理第" in message and "/" in message:
                    try:
                        current, total = map(int, message.split('(')[1].split(')')[0].split('/'))
                        op_mode = self.operation_mode.get()
                        base, span = (20, 60)
                        if op_mode == "2": base, span = (20, 75)
                        elif op_mode == "3" and ("B到C" in message): base, span = (80, 15)
                        self.update_progress(base + int((current / total) * span), f"处理中 {current}/{total}...")
                    except (ValueError, IndexError): pass
                elif "A到B映射完成" in message: self.update_progress(80, "A→B映射完成")
                elif "B到C映射完成" in message: self.update_progress(95, "B→C映射完成")
                elif "处理完成" in message: self.update_progress(100, "处理完成")
                elif level == "ERROR": self.update_progress(100, "处理出错")

            dummy_root = None
            try:
                enhanced_log_with_progress("初始化处理环境...")
                dummy_root = ctk.CTkToplevel(); dummy_root.withdraw()
                bom_app = bom_mapping.BomMappingApp(dummy_root)
                bom_app.log = enhanced_log_with_progress
                
                # --- Correctly pass parameters to the backend logic ---
                # Set file paths by getting the string value
                bom_app.source_file.set(self.source_file.get())
                bom_app.target_file.set(self.target_file.get())
                bom_app.third_file.set(self.third_file.get())
                bom_app.purchased_parts_dir.set(self.purchased_parts_dir.get())

                # Set sheet names by getting the string value
                bom_app.source_sheet.set(self.source_sheet.get())
                bom_app.target_sheet.set(self.target_sheet.get())
                bom_app.third_sheet.set(self.third_sheet.get())
                
                # Set other integer parameters
                bom_app.search_mode.set(int(self.search_mode.get()))
                bom_app.operation_mode.set(int(self.operation_mode.get()))
                bom_app.target_start_row.set(self.target_start_row.get())

                if not bom_app.process_mapping():
                    # Check for specific not-found message from the backend
                    if hasattr(bom_app, 'not_found_parts_message') and bom_app.not_found_parts_message:
                        self.save_not_found_parts(bom_app.not_found_parts_message)
                    raise Exception("BOM mapping process returned with an error status.")

                self.log("处理完成！", "SUCCESS")
                self.master.after(0, lambda: messagebox.showinfo("成功", "数据处理已成功完成！"))
                self.update_progress(100, "成功")

            except Exception as e:
                import traceback
                error_details = traceback.format_exc()
                self.log(f"发生严重错误: {e}", "ERROR")
                self.log(f"Traceback:\n{error_details}", "ERROR")
                self.master.after(0, lambda: messagebox.showerror("执行错误", f"处理过程中发生严重错误:\n{e}"))
                self.update_progress(100, "失败")
            finally:
                def _finally():
                    self.is_processing = False
                    self.execute_button.configure(state="normal", text="执行映射操作")
                    if dummy_root: dummy_root.destroy()
                if self.winfo_exists(): self.master.after(0, _finally)
        finally:
            pythoncom.CoUninitialize()
    
    def save_last_paths(self):
        try:
            paths = {
                "source_file": self.source_file.get(),
                "target_file": self.target_file.get(),
                "third_file": self.third_file.get(),
                "purchased_parts_dir": self.purchased_parts_dir.get(),
                "source_sheet": self.source_sheet.get(),
                "target_sheet": self.target_sheet.get(),
                "third_sheet": self.third_sheet.get(),
            }
            paths = {k: v for k, v in paths.items() if v}
            with open(self.paths_file, "w", encoding="utf-8") as f:
                json.dump(paths, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log(f"保存路径失败: {e}", "ERROR")

    def load_last_paths(self):
        try:
            if os.path.exists(self.paths_file):
                with open(self.paths_file, "r", encoding="utf-8") as f:
                    paths = json.load(f)
                
                file_to_sheets = {
                    "source_file": ("source_sheet", self.set_source_worksheet),
                    "target_file": ("target_sheet", self.set_target_worksheet),
                    "third_file": ("third_sheet", self.set_third_worksheet),
                }

                for key, (sheet_key, set_ws_func) in file_to_sheets.items():
                    if key in paths and paths[key] and os.path.exists(paths[key]):
                        path = paths[key]
                        getattr(self, key).set(path)
                        self.file_display_vars[key].set(os.path.basename(path))
                        # This will also set the sheet options
                        set_ws_func(path)
                        # Now, try to set the saved sheet
                        if sheet_key in paths and paths[sheet_key]:
                            getattr(self, sheet_key).set(paths[sheet_key])

                if "purchased_parts_dir" in paths and paths["purchased_parts_dir"] and os.path.exists(paths["purchased_parts_dir"]):
                    path = paths["purchased_parts_dir"]
                    self.purchased_parts_dir.set(path)
                    self.file_display_vars["purchased_parts_dir"].set(os.path.basename(path))

                self.log("已加载上次使用的路径", "INFO")
        except Exception as e:
            self.log(f"加载路径失败: {e}", "ERROR")

    def open_file_or_folder(self, path):
        if path and os.path.exists(path):
            try:
                os.startfile(path)
            except Exception as e:
                self.log(f"无法打开路径 '{path}': {e}", "ERROR")
        else:
            self.log(f"路径无效或不存在: '{path}'", "WARNING")

    def save_not_found_parts(self, message):
        self.not_found_parts_message = message

    def save_not_found_parts_file(self):
        if not self.not_found_parts_message:
            messagebox.showinfo("信息", "没有未匹配的零件可供保存。")
            return
        file_path = filedialog.asksaveasfilename(defaultextension=".txt", filetypes=[("Text files", "*.txt")], title="保存未匹配的零件")
        if file_path:
            try:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(self.not_found_parts_message)
                self.log(f"未匹配的零件已保存到: {file_path}", "SUCCESS")
                messagebox.showinfo("成功", "文件已成功保存！")
            except Exception as e:
                self.log(f"保存文件失败: {e}", "ERROR")
                messagebox.showerror("错误", f"保存文件失败: {e}")

    def return_to_main(self):
        if self.is_processing:
            if not messagebox.askyesno("确认", "有任务正在处理中，确定要退出吗？"):
                return
        
        if self.return_callback:
            self.return_callback()
        else:
            # Fallback for running the UI standalone
            self.master.destroy()

    def set_worksheet_options(self, key, path):
        try:
            wb = pd.ExcelFile(path)
            sheet_names = wb.sheet_names
            if not sheet_names:
                sheet_names = ['-']
        except Exception as e:
            self.log(f"无法读取工作表: {e}", "ERROR")
            sheet_names = ['-']
        
        var = getattr(self, f"{key.replace('_file', '')}_sheet")
        
        # Automatically set the default sheet without showing or interacting with a UI menu
        if sheet_names and sheet_names[0] != '-':
            default_idx = 1 if key == "third_file" and len(sheet_names) > 1 else 0
            var.set(sheet_names[default_idx])
            self.log(f"为 {os.path.basename(path)} 自动选择工作表: {sheet_names[default_idx]}", "INFO")
        else:
            var.set('-')
            if sheet_names[0] == '-':
                 self.log(f"警告: 在 {os.path.basename(path)} 中未找到工作表。", "WARNING")
        
        self.save_last_paths()

    def set_source_worksheet(self, file_path): self.set_worksheet_options("source_file", file_path)
    def set_target_worksheet(self, file_path): self.set_worksheet_options("target_file", file_path)
    def set_third_worksheet(self, file_path): self.set_worksheet_options("third_file", file_path)

    def show_help(self):
        messagebox.showinfo("帮助", "这是一个BOM表制作工具...\n\n1. 选择文件...\n2. 设置参数...\n3. 点击执行...")

    def show_history(self):
        history_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "bom_mapping_history.log")
        if not os.path.exists(history_file) or os.path.getsize(history_file) == 0:
            messagebox.showinfo("历史记录", "没有历史操作记录。")
            return
            
        history_window = ctk.CTkToplevel(self)
        history_window.title("历史操作记录")
        history_window.geometry("700x500")
        history_window.transient(self)
        history_window.grab_set()

        textbox = ctk.CTkTextbox(history_window, wrap=tk.WORD, font=("Consolas", 10))
        textbox.pack(fill="both", expand=True, padx=10, pady=10)
        with open(history_file, "r", encoding='utf-8') as f:
            textbox.insert("1.0", f.read())
        textbox.configure(state="disabled")

        ctk.CTkButton(history_window, text="关闭", command=history_window.destroy).pack(pady=10)

    def show_log_viewer(self):
        """打开一个模态窗口来显示处理日志。"""
        if self.log_viewer_window is not None and self.log_viewer_window.winfo_exists():
            self.log_viewer_window.focus()
            return

        # 创建一个半透明的遮罩层以降低主界面亮度
        self.overlay = ctk.CTkToplevel(self.master)
        self.overlay.overrideredirect(True)
        self.overlay.geometry(f"{self.master.winfo_width()}x{self.master.winfo_height()}+{self.master.winfo_x()}+{self.master.winfo_y()}")
        self.overlay.configure(fg_color="black")
        self.overlay.attributes("-alpha", 0.6)
        self.overlay.transient(self.master)

        self.log_viewer_window = ctk.CTkToplevel(self)
        self.log_viewer_window.title("处理日志")
        self.log_viewer_window.geometry("800x600")
        self.log_viewer_window.after(10, self._center_toplevel, self.log_viewer_window)

        self.log_viewer_window.transient(self.master)
        self.log_viewer_window.lift()
        self.log_viewer_window.grab_set()

        # 使用文本框来显示日志
        self.log_textbox = ctk.CTkTextbox(self.log_viewer_window, wrap=tk.WORD, font=("Consolas", 11), activate_scrollbars=True)
        self.log_textbox.pack(fill="both", expand=True, padx=10, pady=10)

        # 配置日志级别颜色
        self.log_textbox.tag_config("TIMESTAMP", foreground=self.COLOR_TEXT_TIMESTAMP, font=("Consolas", 10))
        self.log_textbox.tag_config("INFO", foreground=self.COLOR_TEXT)
        self.log_textbox.tag_config("SUCCESS", foreground=self.COLOR_SUCCESS)
        self.log_textbox.tag_config("WARNING", foreground=self.COLOR_WARNING)
        self.log_textbox.tag_config("ERROR", foreground=self.COLOR_ERROR)

        # 填充历史日志
        self.log_textbox.configure(state="normal")
        for timestamp, message, level in self.log_history:
            self.log_textbox.insert("end", f"{timestamp}\n", "TIMESTAMP")
            self.log_textbox.insert("end", f"    {message}\n\n", level)
        self.log_textbox.configure(state="disabled")
        self.log_textbox.see("end")

        self.log_viewer_window.protocol("WM_DELETE_WINDOW", self.close_log_viewer)

    def close_log_viewer(self):
        """关闭日志查看器并释放资源。"""
        if self.overlay:
            self.overlay.destroy()
            self.overlay = None
        if self.log_viewer_window:
            self.log_viewer_window.grab_release()
            self.log_viewer_window.destroy()
            self.log_viewer_window = None
            self.log_textbox = None

    def _center_toplevel(self, toplevel):
        toplevel.update_idletasks()
        parent_x = self.winfo_rootx()
        parent_y = self.winfo_rooty()
        parent_width = self.winfo_width()
        parent_height = self.winfo_height()
        
        widget_width = toplevel.winfo_width()
        widget_height = toplevel.winfo_height()
        
        x = parent_x + (parent_width - widget_width) // 2
        y = parent_y + (parent_height - widget_height) // 2
        
        toplevel.geometry(f"{widget_width}x{widget_height}+{x}+{y}")

    def create_stats_display(self, parent):
        stats_frame = ctk.CTkFrame(parent, fg_color="transparent")
        stats_frame.pack(fill="x", padx=10, pady=5)
        stats_frame.grid_columnconfigure((0, 1), weight=1)

        self.create_stat_card(
            stats_frame, 0, 0, "已处理记录", self.stats_processed, self.stats_total, 
            "📊", self.STATS_ICON_BG_PROCESSED, self.STATS_ICON_TEXT_PROCESSED
        )
        self.create_stat_card(
            stats_frame, 0, 1, "成功匹配", self.stats_success, self.stats_match_rate, 
            "✓", self.STATS_ICON_BG_SUCCESS, self.STATS_ICON_TEXT_SUCCESS
        )
        self.create_stat_card(
            stats_frame, 1, 0, "警告记录", self.stats_warning, self.stats_warning_desc, 
            "⚠️", self.STATS_ICON_BG_WARNING, self.STATS_ICON_TEXT_WARNING
        )
        self.create_stat_card(
            stats_frame, 1, 1, "错误记录", self.stats_error, self.stats_error_desc, 
            "✗", self.STATS_ICON_BG_ERROR, self.STATS_ICON_TEXT_ERROR
        )

    def create_stat_card(self, parent, row, col, title, value_var, desc_var, icon, icon_bg, icon_fg):
        card = ctk.CTkFrame(parent, fg_color=self.COLOR_CARD_BG, corner_radius=8, border_width=1, border_color=self.COLOR_BORDER)
        card.grid(row=row, column=col, sticky="nsew", padx=4, pady=4)
        card.grid_columnconfigure(0, weight=1)

        top_frame = ctk.CTkFrame(card, fg_color="transparent")
        top_frame.grid(row=0, column=0, sticky="ew", padx=12, pady=(8, 4))
        top_frame.grid_columnconfigure(0, weight=1)
        
        ctk.CTkLabel(top_frame, text=title, font=("Microsoft YaHei UI", 12), text_color=self.COLOR_TEXT_LIGHT).grid(row=0, column=0, sticky="w")
        
        icon_bg_frame = ctk.CTkFrame(top_frame, fg_color=icon_bg, width=24, height=24, corner_radius=12)
        icon_bg_frame.grid(row=0, column=1, sticky="e")
        icon_bg_frame.pack_propagate(False)
        ctk.CTkLabel(icon_bg_frame, text=icon, font=("Segoe UI Emoji", 12), text_color=icon_fg).pack(expand=True)

        ctk.CTkLabel(card, textvariable=value_var, font=("Microsoft YaHei UI", 28, "bold"), text_color=self.COLOR_TEXT).grid(row=1, column=0, sticky="w", padx=12, pady=0)
        
        ctk.CTkLabel(card, textvariable=desc_var, font=("Microsoft YaHei UI", 11), text_color=self.COLOR_TEXT_LIGHT).grid(row=2, column=0, sticky="w", padx=12, pady=(0, 8))

    def update_total_records(self, file_path):
        """在后台线程中计算并更新总记录数。"""
        def _calculate_and_update():
            try:
                # 只读取A列以提高效率
                df = pd.read_excel(file_path, sheet_name=0, usecols=[0], header=None)
                # .count() 计算非空单元格的数量
                count = df[df.columns[0]].count()
                total_count = count - 1 if count > 0 else 0
                
                def _update_ui():
                    self.stats_total.set(f"总记录数: {total_count}")
                    # 重置其他统计数据
                    self.stats_processed.set("0")
                    self.stats_success.set("0")
                    self.stats_match_rate.set("匹配率: --%")
                    self.stats_warning.set("0")
                    self.stats_error.set("0")

                # 在主线程中安全地更新UI
                if self.winfo_exists():
                    self.master.after(0, _update_ui)
            except Exception as e:
                print(f"无法计算总记录数: {e}")
                if self.winfo_exists():
                    self.master.after(0, lambda: self.stats_total.set("总记录数: 读取失败"))

        Thread(target=_calculate_and_update, daemon=True).start()

def main():
    app = ctk.CTk()
    ModernBomMappingUI(app)
    app.mainloop()

if __name__ == "__main__":
    main() 