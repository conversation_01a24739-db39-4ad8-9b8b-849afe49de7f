<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重型装备数据处理系统 - 新版设计</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap');
        @import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css');

        :root {
            --primary-color: #3a7bd5;
            --primary-gradient: linear-gradient(to right, #3a7bd5, #00d2ff);
            --background-dark: #1e1e2f;
            --background-light: #ffffff;
            --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            --text-primary: #2c3e50;
            --text-secondary: #7f8c8d;
            --border-color: #ecf0f1;
            --border-radius: 12px;
        }

        body {
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 40px 20px;
            background-color: var(--background-dark);
            background-image: linear-gradient(45deg, rgba(255,255,255,0.02) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.02) 50%, rgba(255,255,255,0.02) 75%, transparent 75%, transparent);
            background-size: 50px 50px;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
        }

        .main-container {
            width: 100%;
            max-width: 900px;
            background: var(--background-light);
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            padding: 30px 40px;
            box-sizing: border-box;
            transition: all 0.3s ease;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .header h1 {
            margin: 0;
            font-size: 28px;
            color: var(--text-primary);
            font-weight: 700;
        }

        .header p {
            margin: 5px 0 0;
            color: var(--text-secondary);
            font-size: 16px;
        }

        .step {
            margin-bottom: 35px;
        }

        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .step-number {
            background: var(--primary-gradient);
            color: white;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 18px;
            font-weight: 700;
            margin-right: 15px;
        }

        .step-header h2 {
            margin: 0;
            font-size: 22px;
            color: var(--text-primary);
        }

        .file-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }
        
        .form-group {
            display: flex;
            align-items: center;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            padding: 8px;
            transition: all 0.3s ease;
        }
        
        .form-group:hover {
            border-color: var(--primary-color);
            box-shadow: 0 0 10px rgba(58, 123, 213, 0.1);
        }

        .form-group i {
            font-size: 18px;
            color: var(--primary-color);
            margin: 0 12px;
            width: 20px;
            text-align: center;
        }

        .form-group label {
            font-size: 15px;
            color: var(--text-secondary);
            flex-grow: 1;
            white-space: nowrap;
        }

        .btn-select {
            background: #fff;
            border: 1px solid #ced4da;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            color: var(--text-primary);
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .btn-select:hover {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .keyword-group {
            background-color: #f8f9fa;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
        }
        
        .keyword-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        .keyword-row:last-child { margin-bottom: 0; }

        .keyword-row label {
            font-size: 15px;
            color: var(--text-secondary);
            margin-right: 20px;
        }

        .segmented-control {
            display: flex;
            border-radius: 8px;
            background: #e9ecef;
            padding: 4px;
        }
        
        .segmented-control input[type="radio"] {
            display: none;
        }

        .segmented-control label {
            padding: 8px 15px;
            font-size: 14px;
            cursor: pointer;
            border-radius: 6px;
            color: var(--text-secondary);
            transition: all 0.3s ease;
            margin: 0 !important;
        }

        .segmented-control input[type="radio"]:checked + label {
            background-color: #fff;
            color: var(--primary-color);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .keyword-input {
            flex-grow: 1;
            margin-left: 20px;
        }
        
        .keyword-input input {
            width: 100%;
            padding: 10px 15px;
            border-radius: 6px;
            border: 1px solid #ced4da;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .keyword-input input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.2);
        }

        .action-area {
            text-align: center;
            margin-top: 30px;
        }

        .btn-process {
            font-size: 18px;
            font-weight: 500;
            color: white;
            background: var(--primary-gradient);
            border: none;
            padding: 15px 50px;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(58, 123, 213, 0.3);
        }

        .btn-process:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(58, 123, 213, 0.4);
        }

    </style>
</head>
<body>

    <div class="main-container">
        <header class="header">
            <h1>重型装备数据处理系统</h1>
            <p>Version 2.0 - 智能BOM生成</p>
        </header>

        <div class="step">
            <div class="step-header">
                <div class="step-number">1</div>
                <h2>文件选择</h2>
            </div>
            <div class="file-grid">
                <div class="form-group">
                    <i class="fas fa-database"></i>
                    <label>数据储存:</label>
                    <button class="btn-select">选择文件</button>
                </div>
                 <div class="form-group">
                    <i class="fas fa-file-excel"></i>
                    <label>油箱明细表:</label>
                    <button class="btn-select">选择文件</button>
                </div>
                 <div class="form-group">
                    <i class="fas fa-file-excel"></i>
                    <label>标准件表:</label>
                    <button class="btn-select">选择文件</button>
                </div>
                 <div class="form-group">
                    <i class="fas fa-file-invoice"></i>
                    <label>BOM表:</label>
                    <button class="btn-select">选择文件</button>
                </div>
                 <div class="form-group">
                    <i class="fas fa-folder"></i>
                    <label>DWG文件夹:</label>
                    <button class="btn-select">选择文件夹</button>
                </div>
                 <div class="form-group">
                    <i class="fas fa-folder-open"></i>
                    <label>DXF输出文件夹:</label>
                    <button class="btn-select">选择文件夹</button>
                </div>
            </div>
        </div>

        <div class="step">
            <div class="step-header">
                <div class="step-number">2</div>
                <h2>关键词设置</h2>
            </div>
            <div class="keyword-group">
                <div class="keyword-row">
                    <label>油箱/标准件来源:</label>
                    <div class="segmented-control">
                        <input type="radio" id="source1-auto" name="source1" checked>
                        <label for="source1-auto">从Sheet2 H列</label>
                        <input type="radio" id="source1-manual" name="source1">
                        <label for="source1-manual">手动输入</label>
                    </div>
                </div>
                 <div class="keyword-row keyword-input">
                    <input type="text" placeholder="手动输入时，多个关键词请用英文逗号(,)分隔">
                 </div>
                 <div class="keyword-row" style="margin-top: 25px;">
                    <label>DXF文件来源:</label>
                    <div class="segmented-control">
                        <input type="radio" id="source2-auto" name="source2" checked>
                        <label for="source2-auto">从Sheet2 J列</label>
                        <input type="radio" id="source2-manual" name="source2">
                        <label for="source2-manual">手动输入</label>
                    </div>
                </div>
                <div class="keyword-row keyword-input">
                    <input type="text" placeholder="手动输入时，多个关键词请用英文逗号(,)分隔">
                </div>
            </div>
        </div>
        
        <div class="action-area">
            <button class="btn-process"><i class="fas fa-cogs"></i> 开始处理</button>
        </div>
    </div>

</body>
</html> 