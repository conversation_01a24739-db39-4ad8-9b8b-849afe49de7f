import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk
import datetime
import os

class ModernEquipmentUI:
    def __init__(self, master):
        self.master = master
        master.title("重型装备数据处理系统")

        # 设置现代化的窗口大小和位置
        screen_width = master.winfo_screenwidth()
        screen_height = master.winfo_screenheight()
        window_width = int(screen_width * 0.8)
        window_height = int(screen_height * 0.8)
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        master.geometry(f"{window_width}x{window_height}+{x}+{y}")
        master.minsize(1200, 800)

        # 现代化主题色彩 - 采用Material Design风格
        self.primary_color = "#1976D2"      # 主蓝色
        self.primary_light = "#42A5F5"      # 浅蓝色
        self.primary_dark = "#0D47A1"       # 深蓝色
        self.secondary_color = "#FF5722"    # 橙红色
        self.accent_color = "#4CAF50"       # 绿色
        self.bg_color = "#FAFAFA"           # 浅灰背景
        self.surface_color = "#FFFFFF"      # 白色表面
        self.text_primary = "#212121"       # 主文字色
        self.text_secondary = "#757575"     # 次要文字色
        self.divider_color = "#E0E0E0"      # 分割线色

        # 创建主框架
        self.main_bg = tk.Frame(master, bg=self.bg_color)
        self.main_bg.pack(fill='both', expand=True)

        # 创建头部
        self.create_header()
        
        # 创建内容区域
        self.content_area = tk.Frame(self.main_bg, bg=self.bg_color)
        self.content_area.pack(fill='both', expand=True, padx=20, pady=10)

        # 创建界面组件
        self.create_side_panel()
        self.create_main_content()
        self.create_status_bar()

        # 启动时间显示
        self.update_time()

    def create_header(self):
        # 创建头部框架
        header_frame = tk.Frame(self.main_bg, bg=self.primary_color, height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        header_content = tk.Frame(header_frame, bg=self.primary_color)
        header_content.pack(fill='both', expand=True, padx=30, pady=15)

        # 左侧Logo和公司信息
        left_section = tk.Frame(header_content, bg=self.primary_color)
        left_section.pack(side='left', fill='y')

        # 创建现代化的Logo
        logo_frame = tk.Frame(left_section, bg='white', width=50, height=50)
        logo_frame.pack(side='left', padx=(0, 15))
        logo_frame.pack_propagate(False)
        logo_text = tk.Label(logo_frame, text="天锻", 
                           font=('Microsoft YaHei UI', 16, 'bold'),
                           fg=self.primary_color, bg='white')
        logo_text.place(relx=0.5, rely=0.5, anchor='center')

        # 公司名称
        company_frame = tk.Frame(left_section, bg=self.primary_color)
        company_frame.pack(side='left', fill='y')
        
        company_label = tk.Label(
            company_frame,
            text="天锻重工",
            font=('Microsoft YaHei UI', 18, 'bold'),
            fg='white', bg=self.primary_color
        )
        company_label.pack(anchor='w')
        
        subtitle_label = tk.Label(
            company_frame,
            text="专注于重型装备制造",
            font=('Microsoft YaHei UI', 10),
            fg='white', bg=self.primary_color
        )
        subtitle_label.pack(anchor='w')

        # 右侧标题和版本信息
        right_section = tk.Frame(header_content, bg=self.primary_color)
        right_section.pack(side='right', fill='y')

        title_label = tk.Label(
            right_section,
            text="重型装备数据处理系统",
            font=('Microsoft YaHei UI', 20, 'bold'),
            fg='white', bg=self.primary_color
        )
        title_label.pack(anchor='e')

        version_label = tk.Label(
            right_section,
            text="Version 1.0",
            font=('Microsoft YaHei UI', 10),
            fg='white', bg=self.primary_color
        )
        version_label.pack(anchor='e')

    def create_side_panel(self):
        # 创建现代化侧边面板
        side_frame = tk.Frame(self.content_area, bg=self.surface_color, 
                            relief='solid', bd=1, highlightbackground=self.divider_color)
        side_frame.pack(side='left', fill='y', padx=(0, 15), pady=0)
        side_frame.configure(width=350)
        side_frame.pack_propagate(False)

        # 显示图标代替图片
        icon_label = tk.Label(side_frame, text="🏭", 
                            font=('Microsoft YaHei UI', 48),
                            bg=self.surface_color, fg=self.primary_color)
        icon_label.pack(pady=20)

        # 公司信息卡片
        info_card = tk.Frame(side_frame, bg=self.primary_color, relief='flat', bd=0)
        info_card.pack(fill='x', padx=20, pady=(0, 20))
        
        company_info = tk.Label(
            info_card,
            text="天锻重工\n专注于重型装备制造\n致力于技术创新",
            font=('Microsoft YaHei UI', 12, 'bold'),
            fg='white',
            bg=self.primary_color,
            justify='center',
            pady=15
        )
        company_info.pack()

        # 使用说明标题
        help_title = tk.Label(side_frame, text="📖 使用说明", 
                            font=('Microsoft YaHei UI', 14, 'bold'),
                            bg=self.surface_color, fg=self.primary_color)
        help_title.pack(anchor='w', padx=20, pady=(0, 10))

        # 帮助文本
        help_text = """1. 选择文件
• 数据储存：选择"密封明细统计"文件
• 油箱明细表/标准件表/BOM表：可选择多个Excel文件
• DWG文件：选择DWG文件夹和DXF输出文件夹

2. 配置参数
• 选择关键词来源（自动读取或手动输入）
• 根据需要调整相关设置

3. 开始处理
• 点击"开始处理"按钮
• 等待处理完成

4. 查看结果
• Sheet1的A-D列：提取的零件信息
• Sheet1的F-G列：密封规格统计结果
• 红色背景：表示未找到对应密封规格

5. 注意事项
• 使用前请安装ODAFileConverter软件
• 支持格式：Excel(.xlsx/.xlsm/.xls)，图纸(.dwg)
• 处理前会清空数据区域，请注意备份

如有问题请联系：重型装备设计所-胡猛超"""

        # 创建滚动文本框
        text_frame = tk.Frame(side_frame, bg=self.surface_color)
        text_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))
        
        # 添加滚动条
        scrollbar = tk.Scrollbar(text_frame)
        scrollbar.pack(side='right', fill='y')
        
        help_text_widget = tk.Text(text_frame, 
                               wrap=tk.WORD, 
                               font=('Microsoft YaHei UI', 9),
                               bg=self.surface_color,
                               fg=self.text_primary,
                               relief='flat',
                               bd=0,
                               yscrollcommand=scrollbar.set)
        help_text_widget.pack(fill='both', expand=True)
        scrollbar.config(command=help_text_widget.yview)
        
        help_text_widget.insert('1.0', help_text)
        help_text_widget.configure(state='disabled')

    def create_main_content(self):
        # 主内容区域
        content_frame = tk.Frame(self.content_area, bg=self.bg_color)
        content_frame.pack(side='right', fill='both', expand=True)

        self.create_file_selection_frame(content_frame)
        self.create_keyword_frame(content_frame)
        self.create_process_buttons(content_frame)

    def create_file_selection_frame(self, parent):
        # 文件选择卡片
        file_frame = tk.LabelFrame(
            parent,
            text="📁 文件选择",
            font=('Microsoft YaHei UI', 12, 'bold'),
            fg=self.primary_color,
            bg=self.surface_color,
            relief='flat',
            bd=1
        )
        file_frame.pack(fill='x', pady=(0, 15), padx=10)

        # 文件选择项配置
        file_configs = [
            {"label": "数据储存", "icon": "💾"},
            {"label": "油箱明细表", "icon": "📊"},
            {"label": "标准件表", "icon": "🔧"},
            {"label": "BOM表", "icon": "📋"},
            {"label": "DWG文件夹", "icon": "📂"},
            {"label": "DXF输出文件夹", "icon": "📤"}
        ]

        # 创建网格布局
        for i, config in enumerate(file_configs):
            row = i // 2
            col = i % 2
            
            # 创建文件项容器
            item_frame = tk.Frame(file_frame, bg=self.surface_color, relief='solid', 
                                bd=1, highlightbackground=self.divider_color)
            item_frame.grid(row=row, column=col, sticky='ew', padx=5, pady=5)
            
            # 配置网格权重
            file_frame.grid_columnconfigure(col, weight=1)
            
            # 图标和标签
            header_frame = tk.Frame(item_frame, bg=self.surface_color)
            header_frame.pack(fill='x', padx=15, pady=(10, 5))
            
            icon_label = tk.Label(header_frame, text=config["icon"], 
                                font=('Microsoft YaHei UI', 16),
                                bg=self.surface_color, fg=self.primary_color)
            icon_label.pack(side='left')
            
            title_label = tk.Label(header_frame, text=config["label"], 
                                 font=('Microsoft YaHei UI', 11, 'bold'),
                                 bg=self.surface_color, fg=self.text_primary)
            title_label.pack(side='left', padx=(8, 0))
            
            # 状态标签
            status_label = tk.Label(item_frame, text="未选择", 
                                  font=('Microsoft YaHei UI', 9),
                                  bg=self.surface_color, fg=self.text_secondary)
            status_label.pack(fill='x', padx=15, pady=(0, 5))
            
            # 选择按钮
            select_button = tk.Button(
                item_frame,
                text="选择文件",
                font=('Microsoft YaHei UI', 10),
                bg=self.primary_color,
                fg='white',
                relief='flat',
                bd=0,
                padx=15,
                pady=8,
                cursor='hand2'
            )
            select_button.pack(fill='x', padx=15, pady=(0, 10))

    def create_keyword_frame(self, parent):
        # 参数配置卡片
        keyword_frame = tk.LabelFrame(
            parent,
            text="⚙️ 参数配置",
            font=('Microsoft YaHei UI', 12, 'bold'),
            fg=self.primary_color,
            bg=self.surface_color,
            relief='flat',
            bd=1
        )
        keyword_frame.pack(fill='x', pady=(0, 15), padx=10)

        # 创建两列布局
        left_column = tk.Frame(keyword_frame, bg=self.surface_color)
        left_column.pack(side='left', fill='both', expand=True, padx=(10, 5), pady=10)
        
        right_column = tk.Frame(keyword_frame, bg=self.surface_color)
        right_column.pack(side='right', fill='both', expand=True, padx=(5, 10), pady=10)

        # 左列：油箱明细表/标准件表关键词设置
        self.create_keyword_section(left_column, "📊 油箱明细表/标准件表关键词")
        
        # 右列：DXF关键词设置
        self.create_keyword_section(right_column, "📐 DXF文件关键词")

    def create_keyword_section(self, parent, title):
        # 标题
        title_label = tk.Label(parent, text=title, 
                             font=('Microsoft YaHei UI', 12, 'bold'),
                             bg=self.surface_color, fg=self.primary_color)
        title_label.pack(anchor='w', pady=(0, 10))
        
        # 单选按钮
        radio_frame = tk.Frame(parent, bg=self.surface_color)
        radio_frame.pack(fill='x', pady=(0, 10))
        
        var = tk.StringVar(value="sheet")
        
        auto_radio = tk.Radiobutton(radio_frame, text="从Sheet2读取",
                                  variable=var, value="sheet",
                                  font=('Microsoft YaHei UI', 10),
                                  bg=self.surface_color, fg=self.text_primary,
                                  selectcolor=self.primary_light,
                                  activebackground=self.surface_color)
        auto_radio.pack(anchor='w', pady=2)
        
        manual_radio = tk.Radiobutton(radio_frame, text="手动输入",
                                    variable=var, value="manual",
                                    font=('Microsoft YaHei UI', 10),
                                    bg=self.surface_color, fg=self.text_primary,
                                    selectcolor=self.primary_light,
                                    activebackground=self.surface_color)
        manual_radio.pack(anchor='w', pady=2)
        
        # 输入框
        entry_frame = tk.Frame(parent, bg=self.surface_color)
        entry_frame.pack(fill='x', pady=(5, 0))
        
        entry_label = tk.Label(entry_frame, text="手动输入关键词（多个关键词用逗号分隔）",
                             font=('Microsoft YaHei UI', 9),
                             bg=self.surface_color, fg=self.text_secondary)
        entry_label.pack(anchor='w', pady=(0, 5))
        
        entry = tk.Entry(entry_frame, font=('Microsoft YaHei UI', 10),
                        relief='solid', bd=1, highlightthickness=1,
                        highlightcolor=self.primary_color)
        entry.pack(fill='x')

    def create_process_buttons(self, parent):
        # 按钮容器
        button_container = tk.Frame(parent, bg=self.bg_color)
        button_container.pack(fill='x', pady=20, padx=10)
        
        # 主处理按钮
        process_frame = tk.Frame(button_container, bg=self.surface_color, relief='solid', 
                               bd=1, highlightbackground=self.divider_color)
        process_frame.pack(pady=10)
        
        button_content = tk.Frame(process_frame, bg=self.surface_color)
        button_content.pack(padx=30, pady=20)
        
        # 图标
        icon_label = tk.Label(button_content, text="🚀", 
                            font=('Microsoft YaHei UI', 24),
                            bg=self.surface_color)
        icon_label.pack()
        
        # 主按钮
        process_button = tk.Button(
            button_content,
            text="开始处理",
            font=('Microsoft YaHei UI', 16, 'bold'),
            bg=self.accent_color,
            fg='white',
            relief='flat',
            bd=0,
            padx=40,
            pady=15,
            cursor='hand2'
        )
        process_button.pack(pady=(10, 0))

        # BOM按钮
        bom_frame = tk.Frame(button_container, bg=self.surface_color, relief='solid',
                           bd=1, highlightbackground=self.divider_color)
        bom_frame.pack(pady=(10, 0))
        
        bom_content = tk.Frame(bom_frame, bg=self.surface_color)
        bom_content.pack(padx=20, pady=15)
        
        bom_icon = tk.Label(bom_content, text="📋", 
                          font=('Microsoft YaHei UI', 20),
                          bg=self.surface_color)
        bom_icon.pack()
        
        bom_button = tk.Button(
            bom_content,
            text="BOM表制作工具",
            font=('Microsoft YaHei UI', 14, 'bold'),
            bg=self.secondary_color,
            fg='white',
            relief='flat',
            bd=0,
            padx=30,
            pady=12,
            cursor='hand2'
        )
        bom_button.pack(pady=(8, 0))

    def create_status_bar(self):
        # 创建现代化状态栏
        status_frame = tk.Frame(self.main_bg, bg=self.surface_color, 
                               relief='solid', bd=1, 
                               highlightbackground=self.divider_color)
        status_frame.pack(side='bottom', fill='x')
        
        status_content = tk.Frame(status_frame, bg=self.surface_color)
        status_content.pack(fill='x', padx=20, pady=8)

        # 左侧时间显示
        time_frame = tk.Frame(status_content, bg=self.surface_color)
        time_frame.pack(side='left')
        
        time_icon = tk.Label(time_frame, text="🕒", 
                           font=('Microsoft YaHei UI', 12),
                           bg=self.surface_color)
        time_icon.pack(side='left', padx=(0, 5))
        
        self.time_label = tk.Label(
            time_frame,
            text="",
            font=('Microsoft YaHei UI', 10),
            fg=self.text_secondary,
            bg=self.surface_color
        )
        self.time_label.pack(side='left')

        # 右侧版权信息
        copyright_frame = tk.Frame(status_content, bg=self.surface_color)
        copyright_frame.pack(side='right')
        
        copyright_icon = tk.Label(copyright_frame, text="©", 
                                font=('Microsoft YaHei UI', 12, 'bold'),
                                bg=self.surface_color, fg=self.primary_color)
        copyright_icon.pack(side='left', padx=(0, 5))
        
        copyright_label = tk.Label(
            copyright_frame,
            text="版权所有 © 2025 天锻重工",
            font=('Microsoft YaHei UI', 10),
            fg=self.text_secondary,
            bg=self.surface_color
        )
        copyright_label.pack(side='left')

    def update_time(self):
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=f"当前时间：{current_time}")
        self.master.after(1000, self.update_time)

def main():
    root = tk.Tk()
    app = ModernEquipmentUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
