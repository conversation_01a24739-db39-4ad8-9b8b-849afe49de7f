import os
import pandas as pd
from openpyxl import load_workbook
import tkinter as tk
from tkinter import filedialog, messagebox
from tkinter import ttk
import datetime
import pickle
from copy import copy
import xlwings as xw
from openpyxl.styles import Font, Border, Side, Alignment
from purchased_parts_processor import PurchasedPartsProcessor
from empty_cell_parts_processor import EmptyCellPartsProcessor
import glob
import sys
import re
import traceback


class BomMappingApp:
    def __init__(self, root):
        self.root = root
        self.root.title("BOM表制作工具")
        self.root.geometry("800x700")

        # 源文件和目标文件路径
        self.source_file = tk.StringVar()
        self.target_file = tk.StringVar()
        # 添加第三个文件变量（工作簿C）
        self.third_file = tk.StringVar()
        # 添加外购件库路径变量
        self.purchased_parts_dir = tk.StringVar()

        # 源工作表和目标工作表名称
        self.source_sheet = tk.StringVar(value="Sheet1")
        self.target_sheet = tk.StringVar(value="Sheet1")
        # 添加工作簿C中的工作表
        self.third_sheet = tk.StringVar(value="Sheet2")

        # 目标行起始位置
        self.target_start_row = tk.IntVar(value=5)

        # 操作模式选择
        self.operation_mode = tk.IntVar(value=3)  # 修改为默认模式3：执行全部映射
        
        # 搜索模式变量
        self.search_mode = tk.IntVar(value=2)  # 修改为默认模式2：精确搜索

        # 定义样式颜色
        self.bg_color = "#F8FAFC"
        self.text_color = "#1E293B"
        self.primary_color = "#3B82F6"  # 主色调（蓝色）
        self.selected_bg_color = "#EBF5FF"  # 选中背景色
        self.hover_bg_color = "#F0F7FF"  # 悬停背景色
        self.border_color = "#3B82F6"  # 选中边框颜色

        # 用于保存自制件映射信息的文件路径
        self.mapping_info_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "mapping_info.pkl")

        # 设置默认外购件库路径
        self.default_purchased_parts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "purchased_parts_db")
        if not os.path.exists(self.default_purchased_parts_dir):
            os.makedirs(self.default_purchased_parts_dir)
        self.purchased_parts_dir.set(self.default_purchased_parts_dir)

        # 创建界面
        self.create_widgets()

    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 源文件选择
        ttk.Label(main_frame, text="源数据文件(工作簿A):").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.source_file, width=50).grid(row=0, column=1, pady=5)
        ttk.Button(main_frame, text="浏览...", command=self.browse_source_file).grid(row=0, column=2, pady=5)

        # 源工作表
        ttk.Label(main_frame, text="源工作表名称:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.source_sheet, width=20).grid(row=1, column=1, sticky=tk.W, pady=5)

        # 目标文件选择
        ttk.Label(main_frame, text="目标文件(工作簿B):").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.target_file, width=50).grid(row=2, column=1, pady=5)
        ttk.Button(main_frame, text="浏览...", command=self.browse_target_file).grid(row=2, column=2, pady=5)

        # 目标工作表
        ttk.Label(main_frame, text="目标工作表名称:").grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.target_sheet, width=20).grid(row=3, column=1, sticky=tk.W, pady=5)

        # 第三个文件选择(工作簿C)
        ttk.Label(main_frame, text="第三个文件(工作簿C):").grid(row=4, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.third_file, width=50).grid(row=4, column=1, pady=5)
        ttk.Button(main_frame, text="浏览...", command=self.browse_third_file).grid(row=4, column=2, pady=5)

        # 第三个文件工作表
        ttk.Label(main_frame, text="第三个文件工作表名称:").grid(row=5, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.third_sheet, width=20).grid(row=5, column=1, sticky=tk.W, pady=5)

        # 外购件库路径选择
        ttk.Label(main_frame, text="外购件库路径:").grid(row=6, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.purchased_parts_dir, width=50).grid(row=6, column=1, pady=5)
        ttk.Button(main_frame, text="浏览...", command=self.browse_purchased_parts_dir).grid(row=6, column=2, pady=5)

        # 添加搜索模式选择
        ttk.Label(main_frame, text="特殊外购件搜索模式:").grid(row=7, column=0, sticky=tk.W, pady=5)
        search_mode_frame = tk.Frame(main_frame, bg=self.bg_color)
        search_mode_frame.grid(row=7, column=1, sticky=tk.W, pady=5)
        
        # 创建带样式的单选按钮
        self.mode1_frame = tk.Frame(search_mode_frame, bg=self.selected_bg_color if self.search_mode.get() == 1 else self.bg_color,
                                   highlightthickness=1, highlightbackground=self.border_color if self.search_mode.get() == 1 else self.bg_color,
                                   padx=10, pady=5)
        self.mode1_frame.pack(anchor=tk.W, fill=tk.X, pady=2)
        
        self.mode1_radio = tk.Radiobutton(self.mode1_frame, text="模式1: 模糊搜索（组合B、C、D列）",
                                       variable=self.search_mode, value=1,
                                       bg=self.selected_bg_color if self.search_mode.get() == 1 else self.bg_color,
                                       fg=self.text_color,
                                       selectcolor=self.selected_bg_color,
                                       activebackground=self.hover_bg_color,
                                       command=self.update_radio_styles)
        self.mode1_radio.pack(anchor=tk.W)
        
        self.mode2_frame = tk.Frame(search_mode_frame, bg=self.selected_bg_color if self.search_mode.get() == 2 else self.bg_color,
                                   highlightthickness=1, highlightbackground=self.border_color if self.search_mode.get() == 2 else self.bg_color,
                                   padx=10, pady=5)
        self.mode2_frame.pack(anchor=tk.W, fill=tk.X, pady=2)
        
        self.mode2_radio = tk.Radiobutton(self.mode2_frame, text="模式2: 精确搜索（B、C、D列值作为关键词）",
                                       variable=self.search_mode, value=2,
                                       bg=self.selected_bg_color if self.search_mode.get() == 2 else self.bg_color,
                                       fg=self.text_color,
                                       selectcolor=self.selected_bg_color,
                                       activebackground=self.hover_bg_color,
                                       command=self.update_radio_styles)
        self.mode2_radio.pack(anchor=tk.W)
        
        # 鼠标悬停效果
        self.mode1_frame.bind("<Enter>", lambda e: self.on_hover(self.mode1_frame, self.mode1_radio, 1))
        self.mode1_frame.bind("<Leave>", lambda e: self.on_leave(self.mode1_frame, self.mode1_radio, 1))
        self.mode1_radio.bind("<Enter>", lambda e: self.on_hover(self.mode1_frame, self.mode1_radio, 1))
        self.mode1_radio.bind("<Leave>", lambda e: self.on_leave(self.mode1_frame, self.mode1_radio, 1))
        
        self.mode2_frame.bind("<Enter>", lambda e: self.on_hover(self.mode2_frame, self.mode2_radio, 2))
        self.mode2_frame.bind("<Leave>", lambda e: self.on_leave(self.mode2_frame, self.mode2_radio, 2))
        self.mode2_radio.bind("<Enter>", lambda e: self.on_hover(self.mode2_frame, self.mode2_radio, 2))
        self.mode2_radio.bind("<Leave>", lambda e: self.on_leave(self.mode2_frame, self.mode2_radio, 2))
        
        # 目标起始行
        ttk.Label(main_frame, text="目标起始行:").grid(row=8, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.target_start_row, width=10).grid(row=8, column=1, sticky=tk.W, pady=5)

        # 操作模式选择
        ttk.Label(main_frame, text="操作模式:").grid(row=9, column=0, sticky=tk.W, pady=5)
        operation_frame = tk.Frame(main_frame, bg=self.bg_color)
        operation_frame.grid(row=9, column=1, sticky=tk.W, pady=5)
        
        # 创建带样式的操作模式单选按钮
        self.op_mode1_frame = tk.Frame(operation_frame, bg=self.selected_bg_color if self.operation_mode.get() == 1 else self.bg_color,
                                      highlightthickness=1, highlightbackground=self.border_color if self.operation_mode.get() == 1 else self.bg_color,
                                      padx=10, pady=5)
        self.op_mode1_frame.pack(anchor=tk.W, fill=tk.X, pady=2)
        
        self.op_mode1_radio = tk.Radiobutton(self.op_mode1_frame, 
                             text="第一步: A→B映射（生成自制件和工作簿B的工作表D的数据）",
                             variable=self.operation_mode, value=1,
                             bg=self.selected_bg_color if self.operation_mode.get() == 1 else self.bg_color,
                             fg=self.text_color,
                             selectcolor=self.selected_bg_color,
                             activebackground=self.hover_bg_color,
                             command=self.update_radio_styles)
        self.op_mode1_radio.pack(anchor=tk.W)
        
        self.op_mode2_frame = tk.Frame(operation_frame, bg=self.selected_bg_color if self.operation_mode.get() == 2 else self.bg_color,
                                      highlightthickness=1, highlightbackground=self.border_color if self.operation_mode.get() == 2 else self.bg_color,
                                      padx=10, pady=5)
        self.op_mode2_frame.pack(anchor=tk.W, fill=tk.X, pady=2)
        
        self.op_mode2_radio = tk.Radiobutton(self.op_mode2_frame, 
                             text="第二步: B→C映射（将工作簿B的数据映射到工作簿C）",
                             variable=self.operation_mode, value=2,
                             bg=self.selected_bg_color if self.operation_mode.get() == 2 else self.bg_color,
                             fg=self.text_color,
                             selectcolor=self.selected_bg_color,
                             activebackground=self.hover_bg_color,
                             command=self.update_radio_styles)
        self.op_mode2_radio.pack(anchor=tk.W)
        
        self.op_mode3_frame = tk.Frame(operation_frame, bg=self.selected_bg_color if self.operation_mode.get() == 3 else self.bg_color,
                                      highlightthickness=1, highlightbackground=self.border_color if self.operation_mode.get() == 3 else self.bg_color,
                                      padx=10, pady=5)
        self.op_mode3_frame.pack(anchor=tk.W, fill=tk.X, pady=2)
        
        self.op_mode3_radio = tk.Radiobutton(self.op_mode3_frame, 
                             text="执行全部映射（完成所有映射操作）",
                             variable=self.operation_mode, value=3,
                             bg=self.selected_bg_color if self.operation_mode.get() == 3 else self.bg_color,
                             fg=self.text_color,
                             selectcolor=self.selected_bg_color,
                             activebackground=self.hover_bg_color,
                             command=self.update_radio_styles)
        self.op_mode3_radio.pack(anchor=tk.W)
        
        # 鼠标悬停效果
        self.op_mode1_frame.bind("<Enter>", lambda e: self.on_hover(self.op_mode1_frame, self.op_mode1_radio, 1, is_operation=True))
        self.op_mode1_frame.bind("<Leave>", lambda e: self.on_leave(self.op_mode1_frame, self.op_mode1_radio, 1, is_operation=True))
        self.op_mode1_radio.bind("<Enter>", lambda e: self.on_hover(self.op_mode1_frame, self.op_mode1_radio, 1, is_operation=True))
        self.op_mode1_radio.bind("<Leave>", lambda e: self.on_leave(self.op_mode1_frame, self.op_mode1_radio, 1, is_operation=True))
        
        self.op_mode2_frame.bind("<Enter>", lambda e: self.on_hover(self.op_mode2_frame, self.op_mode2_radio, 2, is_operation=True))
        self.op_mode2_frame.bind("<Leave>", lambda e: self.on_leave(self.op_mode2_frame, self.op_mode2_radio, 2, is_operation=True))
        self.op_mode2_radio.bind("<Enter>", lambda e: self.on_hover(self.op_mode2_frame, self.op_mode2_radio, 2, is_operation=True))
        self.op_mode2_radio.bind("<Leave>", lambda e: self.on_leave(self.op_mode2_frame, self.op_mode2_radio, 2, is_operation=True))
        
        self.op_mode3_frame.bind("<Enter>", lambda e: self.on_hover(self.op_mode3_frame, self.op_mode3_radio, 3, is_operation=True))
        self.op_mode3_frame.bind("<Leave>", lambda e: self.on_leave(self.op_mode3_frame, self.op_mode3_radio, 3, is_operation=True))
        self.op_mode3_radio.bind("<Enter>", lambda e: self.on_hover(self.op_mode3_frame, self.op_mode3_radio, 3, is_operation=True))
        self.op_mode3_radio.bind("<Leave>", lambda e: self.on_leave(self.op_mode3_frame, self.op_mode3_radio, 3, is_operation=True))

        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=11, column=0, columnspan=3, pady=20)

        ttk.Button(button_frame, text="执行选定操作", command=self.process_mapping).pack(side=tk.LEFT, padx=10)
        ttk.Button(button_frame, text="退出", command=self.root.quit).pack(side=tk.LEFT, padx=10)

        # 日志区域
        ttk.Label(main_frame, text="处理日志:").grid(row=12, column=0, sticky=tk.W, pady=5)
        self.log_text = tk.Text(main_frame, height=20, width=80)
        self.log_text.grid(row=13, column=0, columnspan=3, sticky=(tk.W, tk.E))

        # 滚动条
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        scrollbar.grid(row=13, column=3, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
    def update_radio_styles(self):
        """更新单选按钮的样式，根据当前选中状态"""
        # 更新搜索模式样式
        self.mode1_frame.configure(bg=self.selected_bg_color if self.search_mode.get() == 1 else self.bg_color,
                                 highlightbackground=self.border_color if self.search_mode.get() == 1 else self.bg_color)
        self.mode1_radio.configure(bg=self.selected_bg_color if self.search_mode.get() == 1 else self.bg_color)
        
        self.mode2_frame.configure(bg=self.selected_bg_color if self.search_mode.get() == 2 else self.bg_color,
                                 highlightbackground=self.border_color if self.search_mode.get() == 2 else self.bg_color)
        self.mode2_radio.configure(bg=self.selected_bg_color if self.search_mode.get() == 2 else self.bg_color)
        
        # 更新操作模式样式
        self.op_mode1_frame.configure(bg=self.selected_bg_color if self.operation_mode.get() == 1 else self.bg_color,
                                    highlightbackground=self.border_color if self.operation_mode.get() == 1 else self.bg_color)
        self.op_mode1_radio.configure(bg=self.selected_bg_color if self.operation_mode.get() == 1 else self.bg_color)
        
        self.op_mode2_frame.configure(bg=self.selected_bg_color if self.operation_mode.get() == 2 else self.bg_color,
                                    highlightbackground=self.border_color if self.operation_mode.get() == 2 else self.bg_color)
        self.op_mode2_radio.configure(bg=self.selected_bg_color if self.operation_mode.get() == 2 else self.bg_color)
        
        self.op_mode3_frame.configure(bg=self.selected_bg_color if self.operation_mode.get() == 3 else self.bg_color,
                                    highlightbackground=self.border_color if self.operation_mode.get() == 3 else self.bg_color)
        self.op_mode3_radio.configure(bg=self.selected_bg_color if self.operation_mode.get() == 3 else self.bg_color)
    
    def on_hover(self, frame, radio, value, is_operation=False):
        """鼠标悬停效果"""
        current_value = self.operation_mode.get() if is_operation else self.search_mode.get()
        if current_value != value:  # 仅当未选中时显示悬停效果
            frame.configure(bg=self.hover_bg_color)
            radio.configure(bg=self.hover_bg_color)
    
    def on_leave(self, frame, radio, value, is_operation=False):
        """鼠标离开效果"""
        current_value = self.operation_mode.get() if is_operation else self.search_mode.get()
        if current_value != value:  # 恢复未选中状态
            frame.configure(bg=self.bg_color)
            radio.configure(bg=self.bg_color)
        else:  # 恢复选中状态
            frame.configure(bg=self.selected_bg_color)
            radio.configure(bg=self.selected_bg_color)

    def browse_source_file(self):
        filename = filedialog.askopenfilename(
            title="选择源数据文件",
            filetypes=[("Excel files", "*.xlsx *.xls")]
        )
        if filename:
            self.source_file.set(filename)
            # 自动获取工作表名称
            try:
                wb = load_workbook(filename, read_only=True)
                sheet_names = wb.sheetnames
                if sheet_names:
                    self.source_sheet.set(sheet_names[0])
                    self.log(f"检测到工作表: {', '.join(sheet_names)}")
            except Exception as e:
                self.log(f"无法读取工作表信息: {str(e)}")

    def browse_target_file(self):
        filename = filedialog.askopenfilename(
            title="选择目标文件",
            filetypes=[("Excel files", "*.xlsx *.xls")]
        )
        if filename:
            self.target_file.set(filename)
            # 自动获取工作表名称
            try:
                wb = load_workbook(filename, read_only=True)
                sheet_names = wb.sheetnames
                if sheet_names:
                    self.target_sheet.set(sheet_names[0])
                    self.log(f"检测到工作表: {', '.join(sheet_names)}")
            except Exception as e:
                self.log(f"无法读取工作表信息: {str(e)}")

    def browse_third_file(self):
        filename = filedialog.askopenfilename(
            title="选择第三个文件",
            filetypes=[("Excel files", "*.xlsx *.xls")]
        )
        if filename:
            self.third_file.set(filename)
            # 自动获取工作表名称
            try:
                wb = load_workbook(filename, read_only=True)
                sheet_names = wb.sheetnames
                if sheet_names and len(sheet_names) > 1:
                    # 默认选择第二个工作表
                    self.third_sheet.set(sheet_names[1])
                    self.log(f"检测到工作表: {', '.join(sheet_names)}")
                    self.log(f"默认选择第二个工作表: {sheet_names[1]}")
                elif sheet_names:
                    self.third_sheet.set(sheet_names[0])
                    self.log(f"检测到工作表: {', '.join(sheet_names)}")
                    self.log(f"警告: 未找到第二个工作表，默认使用第一个工作表")
            except Exception as e:
                self.log(f"无法读取工作表信息: {str(e)}")

    def browse_purchased_parts_dir(self):
        """选择外购件库文件夹"""
        dirname = filedialog.askdirectory(
            title="选择外购件库路径"
        )
        if dirname:
            self.purchased_parts_dir.set(dirname)
            self.log(f"已选择外购件库路径: {dirname}")
            # 检查文件夹内是否有Excel文件
            excel_files = glob.glob(os.path.join(dirname, "*.xlsx"))
            excel_files.extend(glob.glob(os.path.join(dirname, "*.xls")))
            if excel_files:
                self.log(f"在外购件库中找到{len(excel_files)}个Excel文件")
            else:
                self.log("警告: 所选文件夹内没有Excel文件，请确保外购件库包含所需的Excel文件")

    def log(self, message):
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update()

    def process_mapping(self):
        operation_mode = self.operation_mode.get()
        result = False

        if operation_mode == 1:
            # A→B映射
            self.log("执行A到B的映射...")
            result = self.map_a_to_b()
        elif operation_mode == 2:
            # B→C映射
            self.log("执行B到C的映射...")
            result = self.map_b_to_c()
        elif operation_mode == 3:
            # 执行全部映射
            self.log("执行全部映射...")
            if self.map_a_to_b():
                self.log("A到B映射完成，继续执行B到C映射...")
                result = self.map_b_to_c()
            else:
                result = False
        else:
            messagebox.showerror("错误", "请选择操作模式")
            result = False
            
        return result

    def map_a_to_b(self):
        """执行从工作簿A到工作簿B的映射"""
        source_path = self.source_file.get()
        target_path = self.target_file.get()
        source_sheet_name = self.source_sheet.get()
        target_sheet_name = self.target_sheet.get()
        target_start_row = self.target_start_row.get()

        if not source_path or not target_path:
            messagebox.showerror("错误", "请选择源文件和目标文件")
            return False

        if not source_sheet_name or not target_sheet_name:
            messagebox.showerror("错误", "请指定工作表名称")
            return False

        try:
            self.log(f"开始处理工作簿A到工作簿B的映射...")
            self.log(f"源文件: {source_path} (工作表: {source_sheet_name})")
            self.log(f"目标文件: {target_path} (工作表: {target_sheet_name})")
            self.log(f"目标起始行: {target_start_row}")

            # 加载源工作簿
            source_wb = load_workbook(source_path)
            if source_sheet_name not in source_wb.sheetnames:
                raise ValueError(f"源工作簿中不存在工作表 '{source_sheet_name}'")
            source_ws = source_wb[source_sheet_name]

            # 加载目标工作簿
            target_wb = load_workbook(target_path)
            if target_sheet_name not in target_wb.sheetnames:
                raise ValueError(f"目标工作簿中不存在工作表 '{target_sheet_name}'")
            target_ws = target_wb[target_sheet_name]

            # 清空目标工作表中的相关单元格
            self.clear_target_cells(target_ws, target_start_row)

            # 处理固定映射关系
            self.map_fixed_cells(source_ws, target_ws)

            # 处理自制件的映射
            self.map_self_made_parts(source_ws, target_ws, target_start_row)

            # 保存目标工作簿
            target_wb.save(target_path)

            # 保存自制件映射信息到文件
            if hasattr(self, 'self_made_parts_mapping') and self.self_made_parts_mapping:
                with open(self.mapping_info_file, 'wb') as f:
                    pickle.dump({
                        'source_file': source_path,
                        'target_file': target_path,
                        'self_made_parts_mapping': self.self_made_parts_mapping
                    }, f)
                self.log(f"已保存自制件映射信息")

            self.log("映射完成！")
            messagebox.showinfo("成功", "工作簿A到工作簿B的映射已完成")
            return True

        except Exception as e:
            self.log(f"处理过程中出错: {str(e)}")
            messagebox.showerror("错误", f"处理失败: {str(e)}")
            return False

    def map_b_to_c(self):
        """执行从工作簿B到工作簿C的映射"""
        target_path = self.target_file.get()  # 工作簿B
        third_path = self.third_file.get()  # 工作簿C
        purchased_parts_dir = self.purchased_parts_dir.get()  # 外购件库路径
        search_mode = self.search_mode.get()  # 获取当前选择的搜索模式
        target_sheet_name = self.target_sheet.get()  # 获取目标工作表名称
        
        if not target_path or not third_path:
            messagebox.showerror("错误", "请选择工作簿B和工作簿C")
            return False
            
        if not os.path.exists(purchased_parts_dir):
            self.log(f"警告: 指定的外购件库路径不存在，将使用默认路径")
            purchased_parts_dir = self.default_purchased_parts_dir
            self.purchased_parts_dir.set(purchased_parts_dir)
            if not os.path.exists(purchased_parts_dir):
                os.makedirs(purchased_parts_dir)
                self.log(f"已创建默认外购件库文件夹: {purchased_parts_dir}")

        try:
            self.log(f"开始处理工作簿B到工作簿C的映射...")
            self.log(f"工作簿B: {target_path}")
            self.log(f"工作簿C: {third_path}")
            self.log(f"特殊外购件搜索模式: {search_mode} ({'模糊搜索' if search_mode == 1 else '精确搜索'})")
            
            # 加载工作簿B（带公式，复制样式用）
            target_wb = load_workbook(target_path)
            if len(target_wb.sheetnames) < 3:
                raise ValueError(f"工作簿B中没有第三个工作表（工作表C）")
            sheet_c = target_wb[target_wb.sheetnames[2]]  # 获取第三个工作表
            sheet_c_name = target_wb.sheetnames[2]
            self.log(f"使用工作簿B中的第三个工作表: {sheet_c_name}")

            # 获取工作簿B的工作表D
            sheet_d_name = "Sheet4"  # 假设工作表D是第四个工作表
            if len(target_wb.sheetnames) < 4:
                # 尝试查找D工作表
                d_sheets = [name for name in target_wb.sheetnames if 'D' in name]
                if d_sheets:
                    sheet_d_name = d_sheets[0]
                    self.log(f"找到工作表D: {sheet_d_name}")
                else:
                    # 尝试查找第四个工作表
                    if len(target_wb.sheetnames) >= 4:
                        sheet_d_name = target_wb.sheetnames[3]
                        self.log(f"使用第四个工作表: {sheet_d_name}")
                    else:
                        raise ValueError(f"工作簿B中没有找到工作表D")
            else:
                sheet_d_name = target_wb.sheetnames[3]  # 获取第四个工作表

            sheet_d = target_wb[sheet_d_name]
            sheet_d_idx = target_wb.sheetnames.index(sheet_d_name)
            self.log(f"使用工作簿B中的工作表D: {sheet_d_name} (索引: {sheet_d_idx})")

            # 获取工作簿B的工作表E
            sheet_e_name = None
            # 尝试查找E工作表
            e_sheets = [name for name in target_wb.sheetnames if 'E' in name]
            if e_sheets:
                sheet_e_name = e_sheets[0]
                self.log(f"找到工作表E: {sheet_e_name}")
            else:
                # 尝试查找第五个工作表
                if len(target_wb.sheetnames) >= 5:
                    sheet_e_name = target_wb.sheetnames[4]
                    self.log(f"使用第五个工作表（作为工作表E）: {sheet_e_name}")
                else:
                    self.log(f"警告: 工作簿B中没有找到工作表E，将跳过工作表E到工作簿C工作表A的映射")

            if sheet_e_name:
                sheet_e = target_wb[sheet_e_name]
                sheet_e_idx = target_wb.sheetnames.index(sheet_e_name)
                self.log(f"使用工作簿B中的工作表E: {sheet_e_name} (索引: {sheet_e_idx})")

            # 用xlwings获取公式结果
            app = xw.App(visible=False)
            try:
                wb_xw = app.books.open(target_path, update_links=False, read_only=True)
                ws_xw = wb_xw.sheets[2]  # 第三个工作表
                f2_value = ws_xw.range('F2').value
                e2_value = ws_xw.range('E2').value
                self.log(f"xlwings读取显示值:")
                self.log(f"F2单元格: {f2_value}")
                self.log(f"E2单元格: {e2_value}")
            except:
                pass

            # 新增：读取BB（第二个工作表）AI2
            ws_xw_bb = wb_xw.sheets[1]  # 第二个工作表
            bb_ai2_value = ws_xw_bb.range('AI2').value
            self.log(f"BB-AI2单元格: {bb_ai2_value}")

            # 新增：读取BB（第二个工作表）AH2，用于新的映射关系
            bb_ah2_value = ws_xw_bb.range('AH2').value
            self.log(f"BB-AH2单元格: {bb_ah2_value}")

            # 使用xlwings读取工作表D的显示值
            ws_xw_d = wb_xw.sheets[sheet_d_idx]
            self.log(f"准备读取工作表D的显示值 (xlwings)")

            # 读取BD-P4单元格的值，用于新的映射关系
            bd_p4_value = ws_xw_d.range('P4').value
            self.log(f"BD-P4单元格: {bd_p4_value}")

            # 使用xlwings读取工作表E的显示值（如果存在）
            ws_xw_e = None
            if sheet_e_name:
                ws_xw_e = wb_xw.sheets[sheet_e_idx]
                self.log(f"准备读取工作表E的显示值 (xlwings)")

            # 加载工作簿C
            third_wb = load_workbook(third_path)
            if len(third_wb.sheetnames) < 2:
                raise ValueError(f"工作簿C中没有第二个工作表（工作表B）")
            third_ws = third_wb[third_wb.sheetnames[1]]  # 获取第二个工作表
            third_sheet_name = third_wb.sheetnames[1]
            self.log(f"使用工作簿C中的第二个工作表: {third_sheet_name}")

            # 获取工作簿C的第一个工作表（工作表A）
            third_ws_a = third_wb[third_wb.sheetnames[0]]  # 获取第一个工作表
            third_sheet_a_name = third_wb.sheetnames[0]
            self.log(f"使用工作簿C中的第一个工作表: {third_sheet_a_name}")

            # 清空工作簿C中的相关单元格
            self.clear_third_cells(third_ws)

            # 尝试加载自制件映射信息
            self_made_parts_mapping = None
            if os.path.exists(self.mapping_info_file):
                try:
                    with open(self.mapping_info_file, 'rb') as f:
                        mapping_info = pickle.load(f)
                        self_made_parts_mapping = mapping_info.get('self_made_parts_mapping')
                        self.log("已加载自制件映射信息")
                except Exception as e:
                    self.log(f"加载自制件映射信息失败: {str(e)}")

            if not self_made_parts_mapping:
                self.log("警告: 未找到自制件映射信息，请先执行A→B映射")
                messagebox.showwarning("警告", "未找到自制件映射信息，请先执行A→B映射")

            # 执行从工作簿B到工作簿C的映射
            self.map_b_to_c_cells(sheet_c, sheet_d, ws_xw_d, f2_value, e2_value, bb_ai2_value, target_wb, third_ws,
                                  self_made_parts_mapping, ws_xw_bb, bb_ah2_value)

            # 执行从工作簿B的工作表E到工作簿C的工作表A的映射
            if sheet_e_name and ws_xw_e and self_made_parts_mapping:
                self.map_b_to_c_sheet_e(ws_xw_e, third_ws_a, self_made_parts_mapping)

                # 新增: 工作簿B的工作表B的AI2单元格的值映射到工作簿C的工作表A的A6单元格
                third_ws_a.cell(row=6, column=1).value = bb_ai2_value  # A列是第1列
                third_ws_a.cell(row=6, column=1).font = Font(name='仿宋', size=10)
                third_ws_a.cell(row=6, column=1).border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                self.log(f"  映射 BB-AI2({bb_ai2_value}) -> CA-A6")

                # 新增: 工作簿B的工作表D的P4单元格的值映射到工作簿C的工作表A的E6和F6单元格
                third_ws_a.cell(row=6, column=5).value = bd_p4_value  # E列是第5列
                third_ws_a.cell(row=6, column=5).font = Font(name='仿宋', size=10)
                third_ws_a.cell(row=6, column=5).border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                self.log(f"  映射 BD-P4({bd_p4_value}) -> CA-E6")

                third_ws_a.cell(row=6, column=6).value = bd_p4_value  # F列是第6列
                third_ws_a.cell(row=6, column=6).font = Font(name='仿宋', size=10)
                third_ws_a.cell(row=6, column=6).border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                self.log(f"  映射 BD-P4({bd_p4_value}) -> CA-F6")

                # 将工作簿C的工作表A的第六行调成20行高
                third_ws_a.row_dimensions[6].height = 20
                self.log(f"  设置 CA第6行的行高为20")

                # 新增: 在工作簿C的工作表A的CX6单元格中填入K03
                # CX列对应的列索引：C=3, X=24，双字母列CX=3*26+24=102
                third_ws_a.cell(row=6, column=102).value = "K03"
                third_ws_a.cell(row=6, column=102).font = Font(name='仿宋', size=10)
                third_ws_a.cell(row=6, column=102).border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                self.log(f"  在 CA-CX6 单元格填入 'K03'")

            # 设置工作簿C的工作表B的A-T列（从第十行开始）单元格的边框样式
            self.set_borders_and_row_height(third_ws)

            # 关闭xlwings
            try:
                wb_xw.close()
                app.quit()
            except:
                pass
            
            # 创建两个列表用于收集未找到匹配的外购件
            not_found_regular_parts = []
            not_found_empty_j_parts = []
            
            # 获取外购件库中的所有Excel文件路径
            excel_files = glob.glob(os.path.join(purchased_parts_dir, "*.xlsx"))
            excel_files.extend(glob.glob(os.path.join(purchased_parts_dir, "*.xls")))
            self.log(f"在外购件库中找到{len(excel_files)}个Excel文件")

            if not excel_files:
                self.log("警告: 外购件库中没有找到Excel文件，跳过外购件处理步骤。")
            else:
                # 处理外购件
                self.log("开始处理外购件...")
                source_path = self.source_file.get()
                source_sheet_name = self.source_sheet.get()
                third_sheet_name = self.third_sheet.get()  # 获取第三个工作表名称

                # 创建外购件处理器
                try:
                    purchased_parts_processor = PurchasedPartsProcessor(excel_files, self.search_mode.get(), self.log)
                    not_found_regular_parts = purchased_parts_processor.process(
                        source_path, target_path, target_wb, target_sheet_name, 
                        third_wb, third_sheet_name, source_sheet_name
                    )
                    self.log("常规外购件处理完成")
                except Exception as e:
                    self.log(f"常规外购件处理过程中出现错误: {e}")
                    import traceback
                    self.log(traceback.format_exc())
                    not_found_regular_parts = []

                # 处理J列为空的外购件
                try:
                    empty_cell_processor = EmptyCellPartsProcessor(excel_files, self.search_mode.get(), self.log)
                    not_found_empty_j_parts = empty_cell_processor.process(
                        source_path, target_path, target_wb, target_sheet_name, 
                        third_wb, third_sheet_name, source_sheet_name
                    )
                    self.log("J列为空的外购件处理完成")
                except Exception as e:
                    self.log(f"J列为空的外购件处理过程中出现错误: {e}")
                    import traceback
                    self.log(traceback.format_exc())
                    not_found_empty_j_parts = []

            # 显示未匹配外购件汇总
            self.show_not_found_summary(not_found_regular_parts, not_found_empty_j_parts)

            # 保存工作簿C - 确保在所有修改完成后才保存
            third_wb.save(third_path)

            self.log("映射完成！")
            messagebox.showinfo("成功", "工作簿B到工作簿C的映射已完成")
            return True
        except Exception as e:
            self.log(f"处理过程中出错: {str(e)}")
            messagebox.showerror("错误", f"处理失败: {str(e)}")
            return False

    def map_b_to_c_sheet_e(self, ws_xw_e, third_ws_a, self_made_parts_mapping):
        """执行从工作簿B的工作表E到工作簿C的工作表A的映射"""
        self.log("处理工作簿B的工作表E到工作簿C的工作表A的映射...")

        try:
            # 定义字体和边框
            fang_song_font = Font(name='仿宋', size=10)

            # 定义虚线边框样式
            dashed_side = Side(style='dashed')
            dashed_border = Border(
                left=dashed_side,
                right=dashed_side,
                top=dashed_side,
                bottom=dashed_side
            )

            # 新增：将工作簿B的工作表E的第六行映射到工作簿C的工作表A的第六行
            self.log("开始映射工作簿B工作表E的第六行到工作簿C工作表A的第六行...")

            # 计算列的索引数
            # A-Z: 1-26
            # AA-AZ: 27-52
            # BA-BZ: 53-78
            # CA-CZ: 79-104
            # DA: 105
            max_col = 105  # DA列的索引是105

            # 列标识符映射函数
            def index_to_column_letter(idx):
                if idx <= 26:
                    return chr(64 + idx)  # A-Z
                else:
                    # 双字母列: AA-ZZ
                    first_letter = chr(64 + (idx - 1) // 26)
                    second_letter = chr(64 + (idx - 1) % 26 + 1)
                    return first_letter + second_letter

            # 记录被映射的行，用于后续设置行高
            mapped_rows = [6]  # 首先添加第6行

            # 映射第六行
            be_row = 6
            ca_row = 6

            # 获取工作簿B工作表E第六行的所有列（从A列到DA列）
            for col_idx in range(1, max_col + 1):
                # 将列索引转换为Excel的列标识符
                col_letter = index_to_column_letter(col_idx)
                cell_address = f"{col_letter}{be_row}"

                try:
                    # 尝试获取单元格的值
                    display_value = ws_xw_e.range(cell_address).value

                    # 如果该列有值，则复制到工作簿C的工作表A
                    if display_value is not None:
                        ca_cell = third_ws_a.cell(row=ca_row, column=col_idx)

                        # 保持数据类型
                        if isinstance(display_value, (int, float)):
                            ca_cell.value = display_value
                        else:
                            ca_cell.value = str(display_value)

                        ca_cell.font = fang_song_font
                        ca_cell.border = dashed_border

                        self.log(f"  复制 BE-{col_letter}{be_row}({display_value}) -> CA-{col_letter}{ca_row}")
                except Exception as e:
                    # 如果出现错误，记录下来但继续处理
                    self.log(f"  警告: 处理单元格 {col_letter}{be_row} 时出错: {str(e)}")

            self.log(f"完成第{be_row}行的映射（A-DA列）")

            # 映射自制件相关行
            for i, (source_row, index) in enumerate(sorted(self_made_parts_mapping.items(), key=lambda x: x[1])):
                # 自制件在工作簿B的工作表E中的行号
                be_row = i + 7  # 第一个自制件从第7行开始
                # 在工作簿C的工作表A中使用相同的行号
                ca_row = be_row

                # 添加到被映射行列表
                mapped_rows.append(ca_row)

                self.log(f"  映射自制件 #{i + 1}: 工作簿B工作表E第{be_row}行 -> 工作簿C工作表A第{ca_row}行")

                # 获取工作簿B工作表E该行的所有列（从A列到DA列）
                for col_idx in range(1, max_col + 1):
                    # 将列索引转换为Excel的列标识符（A、B、C...、AA、AB...、DA等）
                    col_letter = index_to_column_letter(col_idx)
                    cell_address = f"{col_letter}{be_row}"

                    try:
                        # 尝试获取单元格的值
                        display_value = ws_xw_e.range(cell_address).value

                        # 如果该列有值，则复制到工作簿C的工作表A
                        if display_value is not None:
                            ca_cell = third_ws_a.cell(row=ca_row, column=col_idx)

                            # 保持数据类型
                            if isinstance(display_value, (int, float)):
                                ca_cell.value = display_value
                            else:
                                ca_cell.value = str(display_value)

                            ca_cell.font = fang_song_font
                            ca_cell.border = dashed_border

                            # 限制日志输出，只记录有值的单元格，避免日志过多
                            self.log(f"    复制 BE-{col_letter}{be_row}({display_value}) -> CA-{col_letter}{ca_row}")
                    except Exception as e:
                        # 如果出现错误，记录下来但继续处理
                        self.log(f"    警告: 处理单元格 {col_letter}{be_row} 时出错: {str(e)}")

                self.log(f"  完成第{be_row}行的映射（A-DA列）")

            # 设置所有被映射行的行高为20
            for row_idx in mapped_rows:
                third_ws_a.row_dimensions[row_idx].height = 20

            self.log(f"已设置工作簿C工作表A中被映射的行（{', '.join(map(str, mapped_rows))}）的行高为20")
            self.log("工作簿B工作表E到工作簿C工作表A的映射完成")

        except Exception as e:
            self.log(f"映射工作簿B的工作表E到工作簿C的工作表A时出错: {str(e)}")
            raise

    def set_borders_and_row_height(self, worksheet):
        """设置工作表的行高和边框样式"""
        self.log("设置工作表行高和边框样式...")

        # 定义虚线边框样式
        dashed_side = Side(style='dashed')
        dashed_border = Border(
            left=dashed_side,
            right=dashed_side,
            top=dashed_side,
            bottom=dashed_side
        )

        # 定义文本居中对齐样式
        center_alignment = Alignment(horizontal='center', vertical='center')

        # 确定最后一个有数据的行
        last_row = 10  # 初始设置为第10行
        for row in range(worksheet.max_row, 9, -1):
            row_has_data = False
            for col in range(1, 21):  # 检查A-T列
                if worksheet.cell(row=row, column=col).value:
                    row_has_data = True
                    break
            if row_has_data:
                last_row = row
                break

        self.log(f"检测到最后一个有数据的行是第{last_row}行")

        # 如果没有找到数据行，至少确保设置到第20行
        last_row = max(last_row, 20)

        # 设置从第10行到最后一个有数据行的行高为20
        for row_idx in range(10, last_row + 1):
            worksheet.row_dimensions[row_idx].height = 20

        self.log(f"设置了第10行到第{last_row}行的行高为20")

        # 设置A-T列（从第10行到最后一行）单元格的边框样式
        for row_idx in range(10, last_row + 1):
            for col_idx in range(1, 21):  # A到T列（1-20）
                cell = worksheet.cell(row=row_idx, column=col_idx)
                cell.border = dashed_border

        self.log(f"已设置A-T列（从第10行到第{last_row}行）单元格的边框样式为虚线")

        # 设置R-S列（从第9行开始）的单元格文本居中
        for row_idx in range(9, last_row + 1):
            for col_idx in range(18, 20):  # R到S列（18-19）
                cell = worksheet.cell(row=row_idx, column=col_idx)
                cell.alignment = center_alignment

        self.log(f"已设置R-S列（从第9行到第{last_row}行）单元格的文本居中对齐")

    def clear_third_cells(self, third_ws):
        """清空工作簿C中的相关单元格"""
        self.log("清空工作簿C中的相关单元格...")

        # 首先记录并取消所有合并区域
        merged_ranges = list(third_ws.merged_cells.ranges)
        for merged_range in merged_ranges:
            third_ws.unmerge_cells(range_string=str(merged_range))
            self.log(f"  取消合并单元格: {merged_range}")

        # 清空映射的目标单元格
        cells_to_clear = ["L6", "J6", "J7"]

        for cell in cells_to_clear:
            col = self.column_to_index(cell[0])
            row = int(cell[1:])
            third_ws.cell(row=row, column=col).value = None
            self.log(f"  清空单元格: {cell}")

        # 重新合并之前的合并区域
        for merged_range in merged_ranges:
            if ":" in str(merged_range):  # 确保格式正确
                third_ws.merge_cells(range_string=str(merged_range))
                self.log(f"  重新合并单元格: {merged_range}")

    def map_b_to_c_cells(self, sheet_c, sheet_d, ws_xw_d, f2_value, e2_value, bb_ai2_value, target_wb, third_ws,
                         self_made_parts_mapping, ws_xw_bb, bb_ah2_value):
        """执行从工作簿B的工作表C到工作簿C的特定单元格映射，复制公式显示值和样式，并设置仿宋10号字体"""
        self.log("处理工作簿B到工作簿C的映射...")
        try:
            # 定义仿宋10号字体
            fang_song_font = Font(name='仿宋', size=10)

            # 定义边框样式 - 虚线边框
            dashed_side = Side(style='dashed')
            dashed_border = Border(
                left=dashed_side,
                right=dashed_side,
                top=dashed_side,
                bottom=dashed_side
            )

            # BC-F2 -> CB-L6
            source_cell = sheet_c.cell(row=2, column=6)
            target_cell = third_ws.cell(row=6, column=12)
            target_cell.value = str(f2_value) if f2_value is not None else ''
            target_cell.font = fang_song_font
            target_cell.border = dashed_border  # 应用虚线边框
            if source_cell.has_style:
                target_cell.fill = copy(source_cell.fill)
                target_cell.number_format = source_cell.number_format
                target_cell.protection = copy(source_cell.protection)
                target_cell.alignment = copy(source_cell.alignment)
            self.log(f"  复制并粘贴 BC-F2({target_cell.value}) -> CB-L6")

            # BC-E2 -> CB-J6
            source_cell = sheet_c.cell(row=2, column=5)
            target_cell = third_ws.cell(row=6, column=10)
            target_cell.value = str(e2_value) if e2_value is not None else ''
            target_cell.font = fang_song_font
            target_cell.border = dashed_border  # 应用虚线边框
            if source_cell.has_style:
                target_cell.fill = copy(source_cell.fill)
                target_cell.number_format = source_cell.number_format
                target_cell.protection = copy(source_cell.protection)
                target_cell.alignment = copy(source_cell.alignment)
            self.log(f"  复制并粘贴 BC-E2({target_cell.value}) -> CB-J6")

            # BB-AI2(合并单元格) -> CB-J7
            bb_source_sheet = target_wb[target_wb.sheetnames[1]]  # 工作簿B第二个工作表
            bb_source_cell = bb_source_sheet.cell(row=2, column=35)
            cb_target_cell = third_ws.cell(row=7, column=10)
            cb_target_cell.value = str(bb_ai2_value) if bb_ai2_value is not None else ''
            cb_target_cell.font = fang_song_font
            # 为CB-J7设置内边框样式
            inside_side = Side(style='thin')
            inside_border = Border(
                left=None,
                right=None,
                top=None,
                bottom=None,
                diagonal=None,
                diagonal_direction=0,
                outline=False,
                vertical=inside_side,
                horizontal=inside_side
            )
            cb_target_cell.border = inside_border  # 应用内边框样式
            if bb_source_cell.has_style:
                cb_target_cell.fill = copy(bb_source_cell.fill)
                cb_target_cell.number_format = bb_source_cell.number_format
                cb_target_cell.protection = copy(bb_source_cell.protection)
                cb_target_cell.alignment = copy(bb_source_cell.alignment)
            self.log(f"  复制并粘贴 BB-AI2({cb_target_cell.value}) -> CB-J7并应用内边框样式")

            # 新增映射关系 - 从工作簿A到工作簿C
            try:
                # 获取工作簿A
                source_path = self.source_file.get()
                source_sheet_name = self.source_sheet.get()

                if source_path and os.path.exists(source_path):
                    source_wb = load_workbook(source_path, read_only=True)
                    if source_sheet_name in source_wb.sheetnames:
                        source_ws = source_wb[source_sheet_name]

                        # AA-P2 -> CB-H6
                        p2_value = source_ws['P2'].value
                        cb_h6_cell = third_ws.cell(row=6, column=8)  # H列是第8列
                        cb_h6_cell.value = p2_value
                        cb_h6_cell.font = fang_song_font
                        self.log(f"  映射 AA-P2({p2_value}) -> CB-H6")

                        # AA-P10 -> CB-H7
                        p10_value = source_ws['P10'].value
                        cb_h7_cell = third_ws.cell(row=7, column=8)
                        cb_h7_cell.value = p10_value
                        cb_h7_cell.font = fang_song_font
                        self.log(f"  映射 AA-P10({p10_value}) -> CB-H7")

                        # AA-P15 -> CB-M7
                        p15_value = source_ws['P15'].value
                        cb_m7_cell = third_ws.cell(row=7, column=13)  # M列是第13列
                        cb_m7_cell.value = p15_value
                        cb_m7_cell.font = fang_song_font
                        self.log(f"  映射 AA-P15({p15_value}) -> CB-M7")

                        # AA-P16 -> CB-N7
                        p16_value = source_ws['P16'].value
                        cb_n7_cell = third_ws.cell(row=7, column=14)  # N列是第14列
                        cb_n7_cell.value = p16_value
                        cb_n7_cell.font = fang_song_font
                        self.log(f"  映射 AA-P16({p16_value}) -> CB-N7")

                        # 新增: AA-P11 -> CB-R9
                        p11_value = source_ws['P11'].value
                        cb_r9_cell = third_ws.cell(row=9, column=18)  # R列是第18列
                        cb_r9_cell.value = p11_value
                        cb_r9_cell.font = fang_song_font
                        cb_r9_cell.border = dashed_border
                        self.log(f"  映射 AA-P11({p11_value}) -> CB-R9")

                        # 新增: AA-P13 -> CB-S9
                        p13_value = source_ws['P13'].value
                        cb_s9_cell = third_ws.cell(row=9, column=19)  # S列是第19列
                        cb_s9_cell.value = p13_value
                        cb_s9_cell.font = fang_song_font
                        cb_s9_cell.border = dashed_border
                        self.log(f"  映射 AA-P13({p13_value}) -> CB-S9")
                    else:
                        self.log(f"警告: 未在工作簿A中找到工作表 '{source_sheet_name}'")
                else:
                    self.log(f"警告: 无法访问工作簿A（{source_path}）")
            except Exception as e:
                self.log(f"处理工作簿A到工作簿C的映射时出错: {str(e)}")

            # 在CB的固定单元格中填入值
            # CB-O7: 宋秀燕
            third_ws.cell(row=7, column=15).value = "宋秀燕"  # O列是第15列
            third_ws.cell(row=7, column=15).font = fang_song_font

            # CB-P7: 陈海周
            third_ws.cell(row=7, column=16).value = "陈海周"  # P列是第16列
            third_ws.cell(row=7, column=16).font = fang_song_font

            # CB-Q7: 李晨
            third_ws.cell(row=7, column=17).value = "李晨"  # Q列是第17列
            third_ws.cell(row=7, column=17).font = fang_song_font

            # CB-R7: S
            third_ws.cell(row=7, column=18).value = "S"  # R列是第18列
            third_ws.cell(row=7, column=18).font = fang_song_font

            # CB-S7: 当前时间（格式：YYYY/MM/DD）
            current_date = datetime.datetime.now().strftime("%Y/%m/%d")
            third_ws.cell(row=7, column=19).value = current_date  # S列是第19列
            third_ws.cell(row=7, column=19).font = fang_song_font

            self.log(f"  已设置CB-O7到CB-S7单元格的固定值")

            # 新增映射关系 2023-01-21
            # BB-AH2 -> CB-A9 (使用传递进来的参数)
            third_ws.cell(row=9, column=1).value = bb_ah2_value  # A列是第1列
            third_ws.cell(row=9, column=1).font = fang_song_font
            third_ws.cell(row=9, column=1).border = dashed_border
            self.log(f"  映射 BB-AH2({bb_ah2_value}) -> CB-A9")

            # 新需求: BD-B4到BD-G4 -> CB-B9到CB-G9
            for col_idx, col_letter in enumerate(['B', 'C', 'D', 'E', 'F', 'G']):
                # 使用xlwings获取值（保留原始格式）
                bd_value = ws_xw_d.range(f'{col_letter}4').value

                # 映射到CB
                cb_col = col_idx + 2  # B列是第2列
                cb_cell = third_ws.cell(row=9, column=cb_col)

                # 确保保留原始数值类型
                cb_cell.value = bd_value  # 直接赋值，不转换为字符串
                cb_cell.font = fang_song_font
                cb_cell.border = dashed_border

                # 如果是日期格式，确保正确设置
                if isinstance(bd_value, datetime.datetime):
                    cb_cell.number_format = 'yyyy-mm-dd'

                self.log(f"  映射 BD-{col_letter}4({bd_value}) -> CB-{col_letter}9")

            # BB-AI2 -> CB-H9 (使用传递进来的参数)
            third_ws.cell(row=9, column=8).value = bb_ai2_value  # H列是第8列
            third_ws.cell(row=9, column=8).font = fang_song_font
            third_ws.cell(row=9, column=8).border = dashed_border
            self.log(f"  映射 BB-AI2({bb_ai2_value}) -> CB-H9")

            # BD-I4 -> CB-I9
            bd_i4_value = ws_xw_d.range('I4').value
            third_ws.cell(row=9, column=9).value = bd_i4_value  # I列是第9列
            third_ws.cell(row=9, column=9).font = fang_song_font
            third_ws.cell(row=9, column=9).border = dashed_border
            self.log(f"  映射 BD-I4({bd_i4_value}) -> CB-I9")

            # BD-O4 -> CB-J9
            bd_o4_value = ws_xw_d.range('O4').value
            third_ws.cell(row=9, column=10).value = bd_o4_value  # J列是第10列
            third_ws.cell(row=9, column=10).font = fang_song_font
            third_ws.cell(row=9, column=10).border = dashed_border
            self.log(f"  映射 BD-O4({bd_o4_value}) -> CB-J9")

            # BD-P4 -> CB-K9
            bd_p4_value = ws_xw_d.range('P4').value
            third_ws.cell(row=9, column=11).value = bd_p4_value  # K列是第11列
            third_ws.cell(row=9, column=11).font = fang_song_font
            third_ws.cell(row=9, column=11).border = dashed_border
            self.log(f"  映射 BD-P4({bd_p4_value}) -> CB-K9")

            # 合并CB-K9到CB-Q9单元格
            # 先检查是否有合并单元格，如果有则取消
            for merged_cell in list(third_ws.merged_cells.ranges):
                min_row, min_col, max_row, max_col = merged_cell.min_row, merged_cell.min_col, merged_cell.max_row, merged_cell.max_col
                if min_row <= 9 <= max_row and any(min_col <= col <= max_col for col in range(11, 18)):  # K到Q列（11-17）
                    third_ws.unmerge_cells(range_string=str(merged_cell))
                    self.log(f"  取消已有的合并单元格: {merged_cell}")

            # 合并K到Q列
            third_ws.merge_cells(start_row=9, start_column=11, end_row=9, end_column=17)
            self.log(f"  合并单元格 CB-K9:CB-Q9，保留K列的值")

            # ----- 添加工作簿B工作表D到工作簿C工作表B自制件映射的代码 -----
            self.log("开始从工作簿B工作表D映射自制件SAP号和物料信息到工作簿C...")

            # 如果没有自制件映射信息，则退出
            if not self_made_parts_mapping:
                self.log("没有找到自制件映射信息，跳过自制件映射")
                return

            # 按照自制件在原文件中的顺序处理
            for i, (source_row, target_idx) in enumerate(sorted(self_made_parts_mapping.items(), key=lambda x: x[1])):
                # 计算自制件在工作簿B工作表D中的行号
                bd_row = target_idx + 4  # 在工作簿B中，自制件数据从第5行开始，所以加4

                # 计算自制件在工作簿C中的目标行号：10 + (n-1)*2
                # n是自制件的序号，从1开始
                cb_row = source_row+8

                self.log(f"  映射第{i + 1}个自制件: BD行{bd_row} -> CB行{cb_row}")

                # 映射A-I列
                for col_idx in range(1, 10):  # A到I列（1-9）
                    col_letter = chr(64 + col_idx)  # 将数字转换为字母：1->A, 2->B, ...

                    # 使用xlwings获取工作簿B工作表D单元格的显示值
                    try:
                        cell_value = ws_xw_d.range(f'{col_letter}{bd_row}').value

                        # 写入到工作簿C对应单元格
                        cb_cell = third_ws.cell(row=cb_row, column=col_idx)
                        cb_cell.value = cell_value
                        cb_cell.font = fang_song_font
                        cb_cell.border = dashed_border

                        if cell_value is not None:
                            self.log(f"    映射 BD-{col_letter}{bd_row}({cell_value}) -> CB-{col_letter}{cb_row}")
                    except Exception as e:
                        self.log(f"    警告: 处理单元格 BD-{col_letter}{bd_row} 时出错: {str(e)}")

                # 映射O列到J列
                try:
                    o_value = ws_xw_d.range(f'O{bd_row}').value
                    cb_j_cell = third_ws.cell(row=cb_row, column=10)  # J列是第10列
                    cb_j_cell.value = o_value
                    cb_j_cell.font = fang_song_font
                    cb_j_cell.border = dashed_border
                    self.log(f"    映射 BD-O{bd_row}({o_value}) -> CB-J{cb_row}")
                except Exception as e:
                    self.log(f"    警告: 处理单元格 BD-O{bd_row} 时出错: {str(e)}")

                # 映射P列到K-Q列（合并单元格）
                try:
                    p_value = ws_xw_d.range(f'P{bd_row}').value

                    # 检查并取消可能存在的合并单元格
                    for merged_cell in list(third_ws.merged_cells.ranges):
                        min_row, min_col, max_row, max_col = merged_cell.min_row, merged_cell.min_col, merged_cell.max_row, merged_cell.max_col
                        if min_row <= cb_row <= max_row and any(
                                min_col <= col <= max_col for col in range(11, 18)):  # K到Q列（11-17）
                            third_ws.unmerge_cells(range_string=str(merged_cell))
                            self.log(f"    取消已有的合并单元格: {merged_cell}")

                    # 写入P值到K列
                    cb_k_cell = third_ws.cell(row=cb_row, column=11)  # K列是第11列
                    cb_k_cell.value = p_value
                    cb_k_cell.font = fang_song_font

                    # 合并K到Q列
                    third_ws.merge_cells(start_row=cb_row, start_column=11, end_row=cb_row, end_column=17)

                    # 为合并后的单元格设置边框
                    for col in range(11, 18):
                        cell = third_ws.cell(row=cb_row, column=col)
                        cell.border = dashed_border

                    self.log(f"    映射 BD-P{bd_row}({p_value}) -> CB-K{cb_row}:Q{cb_row}(合并单元格)")
                except Exception as e:
                    self.log(f"    警告: 处理单元格 BD-P{bd_row} 时出错: {str(e)}")

                # 设置R和S列的值和样式
                try:
                    # 新增: 从工作簿A中获取E列和G列的值，映射到R列和S列
                    # 获取工作簿A
                    source_path = self.source_file.get()
                    source_sheet_name = self.source_sheet.get()

                    if source_path and os.path.exists(source_path):
                        # 获取自制件在源文件中的原始行号
                        original_row = source_row  # source_row是自制件在工作簿A中的行号

                        try:
                            # 使用带有只读模式的load_workbook加载工作簿A
                            source_wb = load_workbook(source_path, read_only=True)
                            if source_sheet_name in source_wb.sheetnames:
                                source_ws = source_wb[source_sheet_name]

                                # 获取工作簿A中E列和G列的值
                                e_value = source_ws.cell(row=original_row, column=5).value  # E列是第5列
                                g_value = source_ws.cell(row=original_row, column=7).value  # G列是第7列

                                # R列设置为工作簿A的E列值
                                cb_r_cell = third_ws.cell(row=cb_row, column=18)  # R列是第18列
                                cb_r_cell.value = e_value
                                cb_r_cell.font = fang_song_font
                                cb_r_cell.border = dashed_border
                                cb_r_cell.alignment = Alignment(horizontal='center', vertical='center')
                                self.log(f"    映射 AA-E{original_row}({e_value}) -> CB-R{cb_row}")

                                # S列设置为工作簿A的G列值
                                cb_s_cell = third_ws.cell(row=cb_row, column=19)  # S列是第19列
                                cb_s_cell.value = g_value
                                cb_s_cell.font = fang_song_font
                                cb_s_cell.border = dashed_border
                                cb_s_cell.alignment = Alignment(horizontal='center', vertical='center')
                                self.log(f"    映射 AA-G{original_row}({g_value}) -> CB-S{cb_row}")
                            else:
                                self.log(f"    警告: 未在工作簿A中找到工作表 '{source_sheet_name}'")

                                # 如果找不到工作表，设置为空字符串
                                cb_r_cell = third_ws.cell(row=cb_row, column=18)  # R列是第18列
                                cb_r_cell.value = ""
                                cb_r_cell.font = fang_song_font
                                cb_r_cell.border = dashed_border
                                cb_r_cell.alignment = Alignment(horizontal='center', vertical='center')

                                cb_s_cell = third_ws.cell(row=cb_row, column=19)  # S列是第19列
                                cb_s_cell.value = ""
                                cb_s_cell.font = fang_song_font
                                cb_s_cell.border = dashed_border
                                cb_s_cell.alignment = Alignment(horizontal='center', vertical='center')
                        except Exception as e:
                            self.log(f"    警告: 处理自制件在工作簿A中的E列和G列时出错: {str(e)}")

                            # 如果出错，设置为空字符串
                            cb_r_cell = third_ws.cell(row=cb_row, column=18)  # R列是第18列
                            cb_r_cell.value = ""
                            cb_r_cell.font = fang_song_font
                            cb_r_cell.border = dashed_border
                            cb_r_cell.alignment = Alignment(horizontal='center', vertical='center')

                            cb_s_cell = third_ws.cell(row=cb_row, column=19)  # S列是第19列
                            cb_s_cell.value = ""
                            cb_s_cell.font = fang_song_font
                            cb_s_cell.border = dashed_border
                            cb_s_cell.alignment = Alignment(horizontal='center', vertical='center')
                    else:
                        self.log(f"    警告: 无法访问工作簿A（{source_path}）")

                        # 如果无法访问工作簿A，设置为空字符串
                        cb_r_cell = third_ws.cell(row=cb_row, column=18)  # R列是第18列
                        cb_r_cell.value = ""
                        cb_r_cell.font = fang_song_font
                        cb_r_cell.border = dashed_border
                        cb_r_cell.alignment = Alignment(horizontal='center', vertical='center')

                        cb_s_cell = third_ws.cell(row=cb_row, column=19)  # S列是第19列
                        cb_s_cell.value = ""
                        cb_s_cell.font = fang_song_font
                        cb_s_cell.border = dashed_border
                        cb_s_cell.alignment = Alignment(horizontal='center', vertical='center')
                except Exception as e:
                    self.log(f"    警告: 设置R-S列单元格时出错: {str(e)}")

                # 设置T列为空并应用边框
                try:
                    cb_t_cell = third_ws.cell(row=cb_row, column=20)  # T列是第20列
                    cb_t_cell.value = ""
                    cb_t_cell.font = fang_song_font
                    cb_t_cell.border = dashed_border
                except Exception as e:
                    self.log(f"    警告: 设置T列单元格时出错: {str(e)}")

            self.log(f"完成映射 {len(self_made_parts_mapping)} 个自制件的SAP号和物料信息")

        except Exception as e:
            self.log(f"  复制粘贴单元格时出错: {str(e)}")
            raise

    def clear_target_cells(self, target_ws, start_row):
        """清空目标工作表中的相关单元格"""
        self.log("清空目标工作表中的相关单元格...")

        # 首先记录并取消所有合并区域
        merged_ranges = list(target_ws.merged_cells.ranges)
        for merged_range in merged_ranges:
            target_ws.unmerge_cells(range_string=str(merged_range))
            self.log(f"  取消合并单元格: {merged_range}")

        # 清空固定映射的单元格
        fixed_cells = [
            "A2", "B2", "C2", "D2", "G2", "H2",  # P8-P13的映射目标
            "U1", "U2", "U3", "U4", "U5", "U6", "U7", "U8", "U9", "U10"  # P2-P6的映射目标
        ]

        for cell in fixed_cells:
            col = self.column_to_index(cell[0])
            row = int(cell[1:])
            target_ws.cell(row=row, column=col).value = None
            self.log(f"  清空单元格: {cell}")

        # 清空自制件数据区域
        max_row = target_ws.max_row
        for row in range(start_row, max_row + 1):
            # 清空B到H列的数据
            for col in range(2, 9):  # B列到H列
                target_ws.cell(row=row, column=col).value = None

        self.log(f"  清空自制件数据区域: 从第{start_row}行到第{max_row}行")

        # 重新合并之前的合并区域
        for merged_range in merged_ranges:
            if ":" in str(merged_range):  # 确保格式正确
                target_ws.merge_cells(range_string=str(merged_range))
                self.log(f"  重新合并单元格: {merged_range}")

    def map_fixed_cells(self, source_ws, target_ws):
        """处理固定单元格的映射"""
        self.log("处理固定单元格映射...")

        # 映射关系（源单元格 -> 目标单元格）
        mapping = {
            "P2": ["U1", "U2"],  # 产品型号
            "P3": ["U3", "U4"],  # 所属机型
            "P4": ["U5", "U6"],  # 一级部号
            "P5": ["U7", "U8"],  # 二级部分
            "P6": ["U9", "U10"],  # 级部件
            # 新增映射关系
            "P8": ["A2"],  # 映射到A2单元格
            "P9": ["B2"],  # 映射到B2单元格
            "P10": ["C2"],  # 映射到C2单元格
            "P11": ["D2"],  # 映射到D2单元格
            "P12": ["G2"],  # 映射到G2单元格
            "P13": ["H2"],  # 映射到H2单元格
        }

        # 执行映射
        for source_cell, target_cells in mapping.items():
            # 解析源单元格坐标
            source_col = self.column_to_index(source_cell[0])
            source_row = int(source_cell[1:])

            # 获取源单元格的值
            value = source_ws.cell(row=source_row, column=source_col).value
            if value is None:
                value = ""  # 处理空值

            # 判断是单个单元格还是合并单元格
            if len(target_cells) == 1:
                # 单个单元格的情况
                target_cell = target_cells[0]
                target_col = self.column_to_index(target_cell[0])
                target_row = int(target_cell[1:])

                # 记录映射情况
                self.log(f"  映射 {source_cell} -> {target_cell}: {value}")

                # 写入值到目标单元格
                target_ws.cell(row=target_row, column=target_col).value = value
            else:
                # 合并单元格的情况
                # 解析目标单元格坐标
                target_col1 = self.column_to_index(target_cells[0][0])
                target_row1 = int(target_cells[0][1:])

                target_col2 = self.column_to_index(target_cells[1][0])
                target_row2 = int(target_cells[1][1:])

                # 记录映射情况
                self.log(f"  映射 {source_cell} -> {target_cells[0]}-{target_cells[1]}: {value}")

                # 检查并取消任何可能与此区域重叠的合并单元格
                for merged_range in list(target_ws.merged_cells.ranges):
                    min_row = merged_range.min_row
                    max_row = merged_range.max_row
                    min_col = merged_range.min_col
                    max_col = merged_range.max_col

                    # 检查是否有重叠
                    if not (max_row < target_row1 or min_row > target_row2 or
                            max_col < target_col1 or min_col > target_col2):
                        target_ws.unmerge_cells(range_string=str(merged_range))
                        self.log(f"  取消重叠的合并区域: {merged_range}")

                # 写入值到目标单元格
                target_ws.cell(row=target_row1, column=target_col1).value = value

                # 合并目标单元格
                target_ws.merge_cells(
                    start_row=target_row1,
                    start_column=target_col1,
                    end_row=target_row2,
                    end_column=target_col2
                )

    def map_self_made_parts(self, source_ws, target_ws, target_start_row):
        """处理自制件数据的映射"""
        # 寻找属性列的索引（第9列，即I列）
        attribute_col = 9  # I列

        # 获取P8单元格（产品编号前缀）的值
        prefix_col = self.column_to_index('P')
        prefix_row = 8
        prefix_value = source_ws.cell(row=prefix_row, column=prefix_col).value
        if prefix_value is None:
            prefix_value = ""

        self.log(f"产品编号前缀: {prefix_value}")

        # 计数已处理的自制件数量
        processed_count = 0
        skipped_count = 0
        self.log("开始检查自制件...")

        # 当前目标行
        current_target_row = target_start_row

        # 记录自制件在源工作表中的行号和映射后在目标工作表中的行号对应关系
        self.self_made_parts_mapping = {}

        # 遍历源工作表的行
        for row_idx in range(2, source_ws.max_row + 1):  # 从第2行开始是数据
            # 获取属性单元格的值
            attribute_value = source_ws.cell(row=row_idx, column=attribute_col).value

            # 如果属性为空，则认为是自制件
            if attribute_value is None or attribute_value == "":
                # 获取该行的数据
                item_no = source_ws.cell(row=row_idx, column=1).value  # A列，项目号
                name = source_ws.cell(row=row_idx, column=2).value  # B列，名称
                model = source_ws.cell(row=row_idx, column=3).value  # C列，型号
                spec = source_ws.cell(row=row_idx, column=4).value  # D列，规格
                material = source_ws.cell(row=row_idx, column=5).value  # E列，材料
                quantity = source_ws.cell(row=row_idx, column=6).value  # F列，数量
                weight = source_ws.cell(row=row_idx, column=7).value  # G列，重量

                # 始终跳过项目号为空或无效的行
                if item_no is None or item_no == "" or (isinstance(item_no, str) and item_no.lower() == "none"):
                    self.log(f"跳过第{row_idx}行: 项目号为空或无效")
                    skipped_count += 1
                    continue

                # 转换项目号为字符串
                if isinstance(item_no, (int, float)):
                    item_no = str(int(item_no))  # 移除小数点后的零
                else:
                    item_no = str(item_no)

                # 组合项目号（前缀+项目号）
                combined_item_no = f"{prefix_value}{item_no}"

                self.log(f"处理第{row_idx}行的自制件: {name}, 项目号: {combined_item_no}")

                # 映射到目标工作表
                # B5 - 组合项目号
                target_ws.cell(row=current_target_row, column=2).value = combined_item_no
                # C5 - 名称
                target_ws.cell(row=current_target_row, column=3).value = name
                # D5 - 材料
                target_ws.cell(row=current_target_row, column=4).value = material
                # E5 - 规格（如有）
                if spec:
                    target_ws.cell(row=current_target_row, column=5).value = spec
                # F5 - 型号（如有）
                if model:
                    target_ws.cell(row=current_target_row, column=6).value = model
                # G5 - 数量
                target_ws.cell(row=current_target_row, column=7).value = quantity
                # H5 - 重量（如有）
                if weight:
                    target_ws.cell(row=current_target_row, column=8).value = weight

                # 记录自制件在源工作表中的行号和在工作簿B中的行号对应关系
                self.self_made_parts_mapping[row_idx] = current_target_row - target_start_row + 1

                # 移动到下一行
                current_target_row += 1
                processed_count += 1

        if processed_count == 0:
            self.log("未找到任何自制件")
        else:
            self.log(f"共处理了 {processed_count} 个自制件")
            if skipped_count > 0:
                self.log(f"跳过了 {skipped_count} 个项目号无效的行")

    @staticmethod
    def column_to_index(column_letter):
        """将列字母转换为列索引"""
        index = 0
        for char in column_letter:
            index = index * 26 + (ord(char.upper()) - ord('A') + 1)
        return index

    def index_to_column(self, col_idx):
        """将列索引转换为列字母"""
        result = ""
        while col_idx > 0:
            col_idx, remainder = divmod(col_idx - 1, 26)
            result = chr(65 + remainder) + result
        return result

    def show_not_found_summary(self, not_found_regular_parts, not_found_empty_j_parts):
        """
        显示未找到匹配的外购件汇总，并允许导出结果
        
        Args:
            not_found_regular_parts: 未找到匹配的常规外购件列表
            not_found_empty_j_parts: 未找到匹配的特殊外购件列表
        """
        if not not_found_regular_parts and not not_found_empty_j_parts:
            return
            
        # 创建汇总对话框
        summary_dialog = tk.Toplevel(self.root)
        summary_dialog.title("未匹配外购件汇总")
        summary_dialog.geometry("800x600")
        summary_dialog.resizable(True, True)
        summary_dialog.grab_set()
        
        # 设置对话框在屏幕中央
        summary_dialog.update_idletasks()
        width = summary_dialog.winfo_width()
        height = summary_dialog.winfo_height()
        x = (summary_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (summary_dialog.winfo_screenheight() // 2) - (height // 2)
        summary_dialog.geometry('{}x{}+{}+{}'.format(width, height, x, y))
        
        # 创建标题标签
        ttk.Label(summary_dialog, 
                  text=f"未找到匹配的外购件汇总（共{len(not_found_regular_parts) + len(not_found_empty_j_parts)}项）", 
                  font=("宋体", 12, "bold")).pack(pady=10)
        
        # 创建框架来容纳文本框和滚动条
        main_frame = ttk.Frame(summary_dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 创建文本框和滚动条
        text_frame = ttk.Frame(main_frame)
        text_frame.pack(fill="both", expand=True)
        
        txt_result = tk.Text(text_frame, wrap="word", font=("宋体", 10))
        txt_result.pack(side="left", fill="both", expand=True)
        
        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=txt_result.yview)
        scrollbar.pack(side="right", fill="y")
        txt_result.configure(yscrollcommand=scrollbar.set)
        
        # 构建汇总文本
        summary_text = "# 未找到匹配的外购件汇总\n\n"
        summary_text += f"总计: {len(not_found_regular_parts) + len(not_found_empty_j_parts)} 项未匹配\n"
        summary_text += f"- 常规外购件(J列有值): {len(not_found_regular_parts)} 项\n"
        summary_text += f"- 特殊外购件(J列为空): {len(not_found_empty_j_parts)} 项\n\n"
        
        if not_found_regular_parts:
            summary_text += "## 未匹配的常规外购件:\n\n"
            for i, item in enumerate(not_found_regular_parts):
                summary_text += f"{i+1}. 行 {item['row']}: SAP号={item['model']}, 名称={item['c_value']}\n"
            summary_text += "\n"
            
        if not_found_empty_j_parts:
            summary_text += "## 未匹配的特殊外购件:\n\n"
            for i, item in enumerate(not_found_empty_j_parts):
                summary_text += f"{i+1}. 行 {item['row']}: 名称={item['c_value']}, 型号={item['d_value']}\n"
        
        # 显示结果
        txt_result.insert("1.0", summary_text)
        txt_result.configure(state="disabled")  # 设为只读
        
        # 创建按钮框架
        btn_frame = ttk.Frame(summary_dialog)
        btn_frame.pack(pady=15)
        
        # 导出按钮
        def export_results():
            file_path = filedialog.asksaveasfilename(
                title="导出未匹配外购件清单",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt")]
            )
            if file_path:
                try:
                    with open(file_path, "w", encoding="utf-8") as f:
                        f.write(summary_text)
                    messagebox.showinfo("导出成功", f"未匹配外购件清单已导出到: {file_path}")
                except Exception as e:
                    messagebox.showerror("导出失败", f"导出文件时出错: {str(e)}")
        
        ttk.Button(btn_frame, text="导出结果", command=export_results).pack(side="left", padx=10)
        ttk.Button(btn_frame, text="关闭", command=summary_dialog.destroy).pack(side="left", padx=10)
        
        # 等待对话框关闭
        self.root.wait_window(summary_dialog)


def main():
    root = tk.Tk()
    app = BomMappingApp(root)
    root.mainloop()


if __name__ == "__main__":
    main() 