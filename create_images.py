from PIL import Image, ImageDraw, ImageFont, ImageFilter
import os

def create_logo():
    """创建公司logo图片"""
    # 创建一个400x200的透明背景图片
    img = Image.new('RGBA', (400, 200), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制圆形背景
    circle_center = (200, 100)
    circle_radius = 80
    draw.ellipse((circle_center[0] - circle_radius, 
                 circle_center[1] - circle_radius,
                 circle_center[0] + circle_radius,
                 circle_center[1] + circle_radius), 
                fill=(30, 136, 229, 255))  # 蓝色背景
    
    # 绘制内圆
    inner_radius = 70
    draw.ellipse((circle_center[0] - inner_radius, 
                 circle_center[1] - inner_radius,
                 circle_center[0] + inner_radius,
                 circle_center[1] + inner_radius), 
                fill=(25, 118, 210, 255))  # 深蓝色背景
    
    # 尝试加载字体，如果失败则使用默认字体
    try:
        font = ImageFont.truetype("simhei.ttf", 60)  # 使用黑体
    except IOError:
        try:
            font = ImageFont.truetype("arial.ttf", 60)
        except IOError:
            font = ImageFont.load_default()
    
    # 绘制文字
    text = "TZ"
    try:
        # 尝试使用新方法
        bbox = font.getbbox(text)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
    except AttributeError:
        # 回退到旧方法
        try:
            text_width, text_height = font.getsize(text)
        except AttributeError:
            # 如果都不可用，使用估计值
            text_width, text_height = 60, 60
    
    position = (circle_center[0] - text_width // 2, circle_center[1] - text_height // 2 - 10)
    draw.text(position, text, font=font, fill=(255, 255, 255, 255))
    
    # 绘制公司名称
    try:
        small_font = ImageFont.truetype("simhei.ttf", 24)
    except IOError:
        try:
            small_font = ImageFont.truetype("arial.ttf", 24)
        except IOError:
            small_font = ImageFont.load_default()
    
    company_name = "天锻重工"
    try:
        # 尝试使用新方法
        bbox = small_font.getbbox(company_name)
        name_width = bbox[2] - bbox[0]
        name_height = bbox[3] - bbox[1]
    except AttributeError:
        # 回退到旧方法
        try:
            name_width, name_height = small_font.getsize(company_name)
        except AttributeError:
            # 如果都不可用，使用估计值
            name_width, name_height = 100, 24
    
    name_position = (circle_center[0] - name_width // 2, circle_center[1] + circle_radius + 10)
    draw.text(name_position, company_name, font=small_font, fill=(30, 136, 229, 255))
    
    # 保存图片
    img.save("logo.png")
    print("Logo创建成功: logo.png")

def create_hydraulic_press():
    """创建液压机图片"""
    # 创建一个600x500的图片
    img = Image.new('RGB', (600, 500), (255, 255, 255))
    draw = ImageDraw.Draw(img)
    
    # 绘制机器底座
    draw.rectangle((150, 400, 450, 450), fill=(80, 80, 80), outline=(40, 40, 40), width=2)
    
    # 绘制机器立柱
    draw.rectangle((150, 150, 200, 400), fill=(60, 60, 60), outline=(30, 30, 30), width=2)
    draw.rectangle((400, 150, 450, 400), fill=(60, 60, 60), outline=(30, 30, 30), width=2)
    
    # 绘制机器顶部
    draw.rectangle((130, 100, 470, 150), fill=(80, 80, 80), outline=(40, 40, 40), width=2)
    
    # 绘制液压缸
    draw.rectangle((250, 150, 350, 300), fill=(30, 136, 229), outline=(25, 118, 210), width=2)
    
    # 绘制活塞
    draw.rectangle((230, 300, 370, 350), fill=(40, 40, 40), outline=(20, 20, 20), width=2)
    
    # 绘制工作台
    draw.rectangle((200, 350, 400, 380), fill=(120, 120, 120), outline=(80, 80, 80), width=2)
    
    # 绘制控制面板
    draw.rectangle((480, 250, 550, 350), fill=(200, 200, 200), outline=(150, 150, 150), width=2)
    
    # 绘制按钮
    for i in range(3):
        y_pos = 270 + i * 25
        draw.ellipse((495, y_pos, 510, y_pos + 15), fill=(255, 0, 0) if i == 0 else (0, 255, 0))
        draw.rectangle((520, y_pos, 535, y_pos + 15), fill=(0, 0, 255))
    
    # 绘制液压管路
    draw.line((350, 200, 480, 200), fill=(0, 0, 0), width=3)
    draw.line((480, 200, 480, 250), fill=(0, 0, 0), width=3)
    
    # 绘制机器名称
    try:
        font = ImageFont.truetype("simhei.ttf", 24)
    except IOError:
        try:
            font = ImageFont.truetype("arial.ttf", 24)
        except IOError:
            font = ImageFont.load_default()
    
    machine_name = "重型液压机"
    draw.text((250, 50), machine_name, font=font, fill=(0, 0, 0))
    
    # 应用轻微模糊效果，使图像看起来更平滑
    img = img.filter(ImageFilter.SMOOTH)
    
    # 保存图片
    img.save("hydraulic_press.png")
    print("液压机图片创建成功: hydraulic_press.png")

if __name__ == "__main__":
    create_logo()
    create_hydraulic_press()
    print("所有图片创建完成！") 