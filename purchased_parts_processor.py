import os
import glob
import pandas as pd
from openpyxl import load_workbook
import tkinter as tk
from tkinter import messagebox
import xlwings as xw
from openpyxl.styles import Font, Border, Side, Alignment


class PurchasedPartsProcessor:
    def __init__(self, excel_files, search_mode, log_callback, purchased_parts_dir=None):
        self.excel_files = excel_files
        self.search_mode = search_mode
        self.log_callback = log_callback
        self.not_found_items = []
        
        # 外购件库文件夹位置
        if purchased_parts_dir and os.path.exists(purchased_parts_dir):
            self.purchased_parts_dir = purchased_parts_dir
            self.log(f"使用指定的外购件库路径: {purchased_parts_dir}")
        else:
            self.purchased_parts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "purchased_parts_db")
            self.log(f"使用默认外购件库路径: {self.purchased_parts_dir}")
            if not os.path.exists(self.purchased_parts_dir):
                os.makedirs(self.purchased_parts_dir)
                self.log("已创建外购件库文件夹")
    
    def log(self, message):
        """记录日志"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def process(self, source_file, target_file, target_wb, target_sheet_name, third_wb, third_sheet_name, source_sheet_name="Sheet1"):
        self.log("开始处理常规外购件...")
        self.not_found_items = []
        
        try:
            # 使用传入的工作簿对象
            target_ws = target_wb[target_sheet_name]
            third_ws = third_wb[third_sheet_name]
            
            # 清空未匹配项列表
            self.not_found_items = []
            
            # 加载工作簿
            source_wb = load_workbook(source_file, read_only=True)
            if source_sheet_name not in source_wb.sheetnames:
                raise ValueError(f"源工作簿中不存在工作表 '{source_sheet_name}'")
            
            # 用pandas加载源工作表以便于数据处理
            df_source = pd.read_excel(source_file, sheet_name=source_sheet_name)
            
            # 查找外购件
            purchased_parts_rows = []
            for idx, row in df_source.iterrows():
                # 行号是索引+2（Excel从1开始计数，且有表头）
                excel_row = idx + 2
                # 检查I列是否为"外购件"
                attribute_col = 8  # I列索引为8 (0-based)
                if idx < len(df_source) and attribute_col < len(df_source.columns):
                    attribute_value = str(df_source.iloc[idx, attribute_col]).strip() if not pd.isna(df_source.iloc[idx, attribute_col]) else ""
                    self.log(f"检查第{excel_row}行，I列值: '{attribute_value}'")
                    if attribute_value == "外购件":
                        purchased_parts_rows.append(excel_row)
                        self.log(f"找到外购件，位于第{excel_row}行")
            
            if not purchased_parts_rows:
                self.log("未发现外购件")
                return []
            
            self.log(f"总共找到{len(purchased_parts_rows)}个外购件，位于以下行: {purchased_parts_rows}")
                
            # 使用构造函数传入的excel_files作为外购件库文件
            db_files = self.excel_files
            if not db_files:
                self.log("警告: 外购件库中没有找到Excel文件")
            
            # 创建SAP号前缀到特定文件的映射
            prefix_to_file_mapping = {}
            for db_file in db_files:
                filename = os.path.basename(db_file).lower()
                if "液压外购件新增登记表2025" in filename:
                    prefix_to_file_mapping["3FWYY"] = db_file
                elif "软管新增登记表2024" in filename:
                    prefix_to_file_mapping["3FWRG"] = db_file
                elif "通用件库" in filename:
                    # 通用件库对应两个前缀
                    prefix_to_file_mapping["3FT"] = db_file
                    prefix_to_file_mapping["2FT"] = db_file
            
            self.log("已建立SAP号前缀到外购件库文件的映射:")
            for prefix, file in prefix_to_file_mapping.items():
                self.log(f"  前缀 '{prefix}' -> 文件 '{os.path.basename(file)}'")
            
            # 使用xlwings获取工作簿B中的特定单元格值
            app = xw.App(visible=False)
            wb_xw = app.books.open(target_file)
            ws_xw_bb = wb_xw.sheets[1]  # 第二个工作表 (BB)
            
            # 获取BB-AI2和BB-AK2的值
            bb_ai2_value = ws_xw_bb.range('AI2').value  # 用于A列
            bb_ak2_value = ws_xw_bb.range('AK2').value  # 用于J列
            
            self.log(f"BB-AI2单元格: {bb_ai2_value}")
            self.log(f"BB-AK2单元格: {bb_ak2_value}")
            
            # 处理每个外购件
            processed_count = 0
            
            for i, row_num in enumerate(purchased_parts_rows):
                # 新的计算方式：外购件所在行号+8
                cb_row = row_num + 8
                
                self.log(f"开始处理第{i+1}/{len(purchased_parts_rows)}个外购件 (行{row_num} -> CB行{cb_row})")
                
                # 根据行号计算源工作表中的索引位置
                source_idx = row_num - 2
                
                # 获取J列的型号值（用于匹配外购件库）
                model_value = None
                try:
                    if source_idx < len(df_source) and 9 < len(df_source.columns):
                        model_value = df_source.iloc[source_idx, 9]  # J列索引为9 (0-based)
                        self.log(f"  读取J列值: {model_value}")
                    else:
                        self.log(f"  警告: 第{row_num}行的J列不存在")
                except Exception as e:
                    self.log(f"  警告: 读取第{row_num}行的J列时出错: {str(e)}")
                
                if pd.isna(model_value):
                    self.log(f"  第{row_num}行的J列为空，跳过该外购件")
                    continue
                
                model_value = str(model_value).strip()
                self.log(f"处理第{i+1}个外购件(第{row_num}行): 型号={model_value}")
                
                # 确定要搜索的外购件库文件
                target_db_files = []
                
                # 根据SAP号前缀确定搜索的文件
                if len(model_value) >= 5:
                    prefix_5 = model_value[:5].upper()  # 取前5位并转为大写
                    if prefix_5 in prefix_to_file_mapping:
                        target_db_files.append(prefix_to_file_mapping[prefix_5])
                        self.log(f"  根据前缀'{prefix_5}'确定搜索文件: {os.path.basename(prefix_to_file_mapping[prefix_5])}")
                
                if len(model_value) >= 3 and not target_db_files:
                    prefix_3 = model_value[:3].upper()  # 取前3位并转为大写
                    if prefix_3 in prefix_to_file_mapping:
                        target_db_files.append(prefix_to_file_mapping[prefix_3])
                        self.log(f"  根据前缀'{prefix_3}'确定搜索文件: {os.path.basename(prefix_to_file_mapping[prefix_3])}")
                
                # 如果没有找到匹配的前缀，则搜索所有文件
                if not target_db_files:
                    self.log(f"  SAP号'{model_value}'不匹配任何已知前缀，将搜索所有外购件库文件")
                    target_db_files = db_files
                
                # 在外购件库中查找匹配的型号
                match_found = False
                for db_file in target_db_files:
                    if match_found:
                        break
                        
                    try:
                        # 读取外购件库文件中的所有工作表
                        db_wb = load_workbook(db_file, read_only=True)
                        
                        for sheet_name in db_wb.sheetnames:
                            # 使用pandas读取工作表
                            df_db = pd.read_excel(db_file, sheet_name=sheet_name)
                            
                            # 在B列中查找匹配的型号
                            for db_idx, db_row in df_db.iterrows():
                                if 1 < len(df_db.columns) and not pd.isna(df_db.iloc[db_idx, 1]):  # B列索引为1 (0-based)
                                    db_model = str(df_db.iloc[db_idx, 1]).strip()
                                    
                                    if db_model == model_value:
                                        self.log(f"  在外购件库中找到匹配: {os.path.basename(db_file)}, 工作表: {sheet_name}, 行: {db_idx+2}")
                                        match_found = True
                                        
                                        # 第一步: 映射外购件库中的B列值到CB-Hx
                                        third_ws.cell(row=cb_row, column=8).value = db_model
                                        third_ws.cell(row=cb_row, column=8).font = Font(name='仿宋', size=10)
                                        
                                        # 映射外购件库中的C列值到CB-Kx
                                        if 2 < len(df_db.columns):  # 确保C列存在
                                            c_value = df_db.iloc[db_idx, 2] if not pd.isna(df_db.iloc[db_idx, 2]) else ""
                                            third_ws.cell(row=cb_row, column=11).value = c_value
                                            third_ws.cell(row=cb_row, column=11).font = Font(name='仿宋', size=10)
                                            
                                            # 合并CB-Kx到CB-Qx
                                            third_ws.merge_cells(start_row=cb_row, start_column=11, end_row=cb_row, end_column=17)
                                            self.log(f"  映射 XX-C{db_idx+2}({c_value}) -> CB-K{cb_row}")
                                        
                                        self.log(f"  映射 XX-B{db_idx+2}({db_model}) -> CB-H{cb_row}")
                                        break
                            
                            if match_found:
                                break
                    except Exception as e:
                        self.log(f"  读取外购件库文件 {os.path.basename(db_file)} 时出错: {str(e)}")
                
                if not match_found:
                    self.log(f"  警告: 未在外购件库中找到匹配的型号: {model_value}")
                    # 记录未找到匹配的项目
                    not_found_info = {
                        'row': row_num,
                        'model': model_value,
                        'b_value': df_source.iloc[source_idx, 1] if not pd.isna(df_source.iloc[source_idx, 1]) else "",  # B列值
                        'c_value': df_source.iloc[source_idx, 2] if not pd.isna(df_source.iloc[source_idx, 2]) else ""   # C列值
                    }
                    self.not_found_items.append(not_found_info)
                else:
                    processed_count += 1
                
                # 第二步: 映射F、E、G列到目标工作表
                # F列（数量）-> CB-Ix
                f_value = df_source.iloc[source_idx, 5]  # F列索引为5 (0-based)
                third_ws.cell(row=cb_row, column=9).value = f_value if not pd.isna(f_value) else ""
                third_ws.cell(row=cb_row, column=9).font = Font(name='仿宋', size=10)
                self.log(f"  映射 AA-F{row_num}({f_value}) -> CB-I{cb_row}")
                
                # E列（材料）-> CB-Rx
                # 修改规则：如果E列为空，则R列填入"组件"
                e_value = df_source.iloc[source_idx, 4]  # E列索引为4 (0-based)
                if pd.isna(e_value) or str(e_value).strip() == "":
                    r_value = "组件"
                    self.log(f"  AA-E{row_num}为空，CB-R{cb_row}填入'组件'")
                else:
                    r_value = e_value
                third_ws.cell(row=cb_row, column=18).value = r_value
                third_ws.cell(row=cb_row, column=18).font = Font(name='仿宋', size=10)
                third_ws.cell(row=cb_row, column=18).alignment = Alignment(horizontal='center', vertical='center')
                self.log(f"  映射 AA-E{row_num}({e_value}) -> CB-R{cb_row}({r_value})")
                
                # G列（重量）-> CB-Sx
                g_value = df_source.iloc[source_idx, 6]  # G列索引为6 (0-based)
                third_ws.cell(row=cb_row, column=19).value = g_value if not pd.isna(g_value) else ""
                third_ws.cell(row=cb_row, column=19).font = Font(name='仿宋', size=10)
                third_ws.cell(row=cb_row, column=19).alignment = Alignment(horizontal='center', vertical='center')
                self.log(f"  映射 AA-G{row_num}({g_value}) -> CB-S{cb_row}")
                
                # 第三步: 映射BB-AI2和BB-AK2到目标工作表
                # BB-AI2 -> CB-Ax
                third_ws.cell(row=cb_row, column=1).value = bb_ai2_value
                third_ws.cell(row=cb_row, column=1).font = Font(name='仿宋', size=10)
                self.log(f"  映射 BB-AI2({bb_ai2_value}) -> CB-A{cb_row}")
                
                # BB-AK2 -> CB-Jx
                third_ws.cell(row=cb_row, column=10).value = bb_ak2_value
                third_ws.cell(row=cb_row, column=10).font = Font(name='仿宋', size=10)
                self.log(f"  映射 BB-AK2({bb_ak2_value}) -> CB-J{cb_row}")
                
                # 设置所有单元格的虚线边框
                dashed_side = Side(style='dashed')
                dashed_border = Border(
                    left=dashed_side,
                    right=dashed_side,
                    top=dashed_side,
                    bottom=dashed_side
                )
                
                # 应用边框到所有单元格
                for col in range(1, 21):  # A到T列
                    cell = third_ws.cell(row=cb_row, column=col)
                    cell.border = dashed_border
                
                # 设置行高为20
                third_ws.row_dimensions[cb_row].height = 20
            
            # 关闭xlwings
            wb_xw.close()
            app.quit()
            
            # 显示未找到匹配的外购件总结
            if self.not_found_items:
                root = tk.Tk()
                root.withdraw()
                
                # 构建消息内容
                message = f"共有{len(self.not_found_items)}个外购件未找到匹配:\n\n"
                for i, item in enumerate(self.not_found_items):  # 显示所有未匹配项
                    message += f"{i+1}. 行{item['row']}: SAP号={item['model']}, 名称={item['c_value']}\n"
                
                messagebox.showwarning("未找到匹配的外购件", message)
                root.destroy()
            
            self.log(f"外购件处理完成，共找到{len(purchased_parts_rows)}个外购件，成功处理{processed_count}个，未找到匹配{len(self.not_found_items)}个")
            return self.not_found_items
            
        except Exception as e:
            self.log(f"处理外购件时出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            return []


def main():
    """测试外购件处理器的主函数"""
    # 测试用的文件路径
    source_file = "path/to/workbook_a.xlsx"
    target_file = "path/to/workbook_b.xlsx"
    third_file = "path/to/workbook_c.xlsx"
    
    # 创建处理器
    processor = PurchasedPartsProcessor(source_file, target_file, third_file)
    
    # 处理外购件
    processor.process_purchased_parts()


if __name__ == "__main__":
    main() 