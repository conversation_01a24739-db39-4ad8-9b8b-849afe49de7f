#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的界面测试
"""

import tkinter as tk
from tkinter import messagebox
import datetime

def create_simple_modern_ui():
    """创建简单的现代化界面测试"""
    root = tk.Tk()
    root.title("重型装备数据处理系统 - 界面测试")
    
    # 设置窗口
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    window_width = int(screen_width * 0.6)
    window_height = int(screen_height * 0.6)
    x = (screen_width - window_width) // 2
    y = (screen_height - window_height) // 2
    root.geometry(f"{window_width}x{window_height}+{x}+{y}")
    
    # 颜色方案
    colors = {
        'primary': '#1976D2',
        'background': '#FAFAFA',
        'surface': '#FFFFFF',
        'text_primary': '#212121',
        'accent': '#4CAF50'
    }
    
    # 主容器
    main_frame = tk.Frame(root, bg=colors['background'])
    main_frame.pack(fill='both', expand=True)
    
    # 头部
    header = tk.Frame(main_frame, bg=colors['primary'], height=60)
    header.pack(fill='x')
    header.pack_propagate(False)
    
    # Logo和标题
    header_content = tk.Frame(header, bg=colors['primary'])
    header_content.pack(expand=True, fill='both', padx=20, pady=10)
    
    # Logo
    logo_frame = tk.Frame(header_content, bg='white', width=40, height=40)
    logo_frame.pack(side='left', padx=(0, 15))
    logo_frame.pack_propagate(False)
    
    logo_text = tk.Label(logo_frame, text="天锻", 
                       font=('Microsoft YaHei UI', 12, 'bold'),
                       fg=colors['primary'], bg='white')
    logo_text.place(relx=0.5, rely=0.5, anchor='center')
    
    # 标题
    title_label = tk.Label(header_content, text="重型装备数据处理系统",
                          font=('Microsoft YaHei UI', 16, 'bold'),
                          fg='white', bg=colors['primary'])
    title_label.pack(side='left', padx=(0, 20))
    
    # 版本信息
    version_label = tk.Label(header_content, text="Version 1.0",
                           font=('Microsoft YaHei UI', 10),
                           fg='white', bg=colors['primary'])
    version_label.pack(side='right')
    
    # 内容区域
    content_area = tk.Frame(main_frame, bg=colors['background'])
    content_area.pack(fill='both', expand=True, padx=20, pady=20)
    
    # 欢迎卡片
    welcome_card = tk.Frame(content_area, bg=colors['surface'], 
                           relief='solid', bd=1, highlightbackground='#E0E0E0')
    welcome_card.pack(fill='both', expand=True)
    
    # 欢迎内容
    welcome_content = tk.Frame(welcome_card, bg=colors['surface'])
    welcome_content.pack(expand=True, fill='both', padx=40, pady=40)
    
    # 图标
    icon_label = tk.Label(welcome_content, text="🏭", 
                         font=('Microsoft YaHei UI', 48),
                         bg=colors['surface'], fg=colors['primary'])
    icon_label.pack(pady=(0, 20))
    
    # 欢迎文本
    welcome_text = tk.Label(welcome_content, 
                           text="欢迎使用重型装备数据处理系统\n\n现代化界面设计测试成功！",
                           font=('Microsoft YaHei UI', 16),
                           bg=colors['surface'], fg=colors['text_primary'],
                           justify='center')
    welcome_text.pack(pady=(0, 30))
    
    # 测试按钮
    test_button = tk.Button(welcome_content, text="界面测试成功 ✓",
                           font=('Microsoft YaHei UI', 14, 'bold'),
                           bg=colors['accent'], fg='white',
                           relief='flat', bd=0, padx=30, pady=15,
                           cursor='hand2',
                           command=lambda: messagebox.showinfo("测试", "现代化界面设计成功！"))
    test_button.pack()
    
    # 状态栏
    status_bar = tk.Frame(main_frame, bg=colors['surface'], 
                         relief='solid', bd=1, highlightbackground='#E0E0E0')
    status_bar.pack(side='bottom', fill='x')
    
    status_content = tk.Frame(status_bar, bg=colors['surface'])
    status_content.pack(fill='x', padx=20, pady=8)
    
    # 时间显示
    time_label = tk.Label(status_content, text="",
                         font=('Microsoft YaHei UI', 10),
                         fg='#757575', bg=colors['surface'])
    time_label.pack(side='left')
    
    # 版权信息
    copyright_label = tk.Label(status_content, text="版权所有 © 2025 天锻重工",
                              font=('Microsoft YaHei UI', 10),
                              fg='#757575', bg=colors['surface'])
    copyright_label.pack(side='right')
    
    # 更新时间
    def update_time():
        try:
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            time_label.config(text=f"当前时间：{current_time}")
            root.after(1000, update_time)
        except:
            pass
    
    update_time()
    
    return root

def main():
    """主函数"""
    try:
        print("启动界面测试...")
        root = create_simple_modern_ui()
        print("界面创建成功，启动主循环...")
        root.mainloop()
        print("程序正常退出")
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
