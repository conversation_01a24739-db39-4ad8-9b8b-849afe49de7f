import os
import glob
import pandas as pd
from openpyxl import load_workbook
import tkinter as tk
from tkinter import messagebox, simpledialog, filedialog, Toplevel, Listbox, Button, Label, Scrollbar, Frame
import xlwings as xw
from openpyxl.styles import Font, Border, Side, Alignment
import openpyxl
from tkinter import *
from tkinter import ttk


class EmptyCellPartsProcessor:
    def __init__(self, excel_files, search_mode, log_callback, purchased_parts_dir=None):
        """
        初始化J列为空的外购件处理器
        
        Args:
            excel_files: 外购件库Excel文件列表
            search_mode: 搜索模式 (1: 模糊搜索, 2: 精确搜索)
            log_callback: 用于记录日志的回调函数
            purchased_parts_dir: 外购件库文件夹路径
        """
        self.excel_files = excel_files
        self.search_mode = search_mode  # 添加搜索模式属性
        self.log_callback = log_callback
        self.not_found_items = []  # 存储未找到匹配的外购件
        
        # 外购件库文件夹位置
        if purchased_parts_dir and os.path.exists(purchased_parts_dir):
            self.purchased_parts_dir = purchased_parts_dir
            self.log(f"使用指定的外购件库路径: {purchased_parts_dir}")
        else:
            self.purchased_parts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "purchased_parts_db")
            self.log(f"使用默认外购件库路径: {self.purchased_parts_dir}")
            if not os.path.exists(self.purchased_parts_dir):
                os.makedirs(self.purchased_parts_dir)
                self.log("已创建外购件库文件夹")
        
    def log(self, message):
        """记录日志"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)
    
    def select_purchased_parts_dir(self):
        """让用户选择外购件库文件夹路径"""
        root = tk.Tk()
        root.withdraw()  # 不显示主窗口
        
        dirname = filedialog.askdirectory(
            title="选择外购件库路径"
        )
        if dirname:
            self.purchased_parts_dir = dirname
            self.log(f"已选择外购件库路径: {dirname}")
            return True
        else:
            self.log("未选择外购件库路径")
            return False
    
    def fill_fixed_values(self, third_ws):
        """
        在工作簿C的工作表B的B-F列填入固定值，并在G列填入序号
        
        Args:
            third_ws: 工作簿C的工作表B对象
        """
        try:
            self.log("开始在工作簿C的工作表B填入固定值...")
            
            # 确定最后一个有数据的行
            last_row = 9  # 从第9行开始
            for row in range(third_ws.max_row, 8, -1):
                row_has_data = False
                for col in range(1, 21):  # 检查A-T列
                    if third_ws.cell(row=row, column=col).value:
                        row_has_data = True
                        last_row = row
                        break
                if row_has_data:
                    break
            
            self.log(f"检测到最后一个有数据的行是第{last_row}行")
            
            # 定义要填入的固定值
            fixed_values = {
                2: "3K01",        # B列
                3: "20240101",    # C列
                4: "01",          # D列
                5: "1",           # E列
                6: "1"            # F列
            }
            
            # 设置字体和边框样式
            font = Font(name='仿宋', size=10)
            dashed_side = Side(style='dashed')
            dashed_border = Border(
                left=dashed_side,
                right=dashed_side,
                top=dashed_side,
                bottom=dashed_side
            )
            
            # 填入固定值（B-F列）
            for row in range(9, last_row + 1):
                for col, value in fixed_values.items():
                    cell = third_ws.cell(row=row, column=col)
                    cell.value = value
                    cell.font = font
                    cell.border = dashed_border
                self.log(f"已在第{row}行填入固定值")
            
            # 填入G列序号（从第10行开始）
            sequence_number = 10  # 起始值为10，对应0010
            for row in range(10, last_row + 1):
                cell = third_ws.cell(row=row, column=7)  # G列是第7列
                # 格式化序号为4位数字字符串
                cell.value = f"{sequence_number:04d}"
                cell.font = font
                cell.border = dashed_border
                self.log(f"在G{row}单元格填入序号: {cell.value}")
                sequence_number += 10  # 增加10
            
            self.log(f"已完成固定值和序号填入，共处理了{last_row - 8}行")
            return True
        except Exception as e:
            self.log(f"填入固定值和序号时出错: {str(e)}")
            return False

    def process(self, source_file, target_file, target_wb, target_sheet_name, third_wb, third_sheet_name, source_sheet_name="Sheet1"):
        """
        处理J列为空的外购件
        
        Args:
            source_file: 工作簿A的路径
            target_file: 工作簿B的路径
            target_wb: 工作簿B的对象
            target_sheet_name: 工作簿B的工作表名称
            third_wb: 工作簿C的对象
            third_sheet_name: 工作簿C的工作表名称
            source_sheet_name: 工作簿A中的工作表名称
            
        Returns:
            list: 未找到匹配的项目列表
        """
        try:
            self.log(f"开始处理J列为空的外购件... (搜索模式: {self.search_mode})")
            self.log(f"搜索模式说明: 1-模糊搜索, 2-精确搜索")
            
            # 清空未匹配项列表
            self.not_found_items = []
            
            # 确保有外购件库文件
            if not self.excel_files:
                self.log("警告: 外购件库中没有找到Excel文件，无法继续处理")
                return []
            
            # 加载工作簿
            source_wb = load_workbook(source_file, read_only=True)
            if source_sheet_name not in source_wb.sheetnames:
                raise ValueError(f"源工作簿中不存在工作表 '{source_sheet_name}'")
            
            # 用pandas加载源工作表以便于数据处理
            df_source = pd.read_excel(source_file, sheet_name=source_sheet_name)
            
            # 查找J列为空的外购件
            empty_j_parts_rows = []
            for idx, row in df_source.iterrows():
                # 行号是索引+2（Excel从1开始计数，且有表头）
                excel_row = idx + 2
                
                # 检查I列是否为"外购件"
                attribute_col = 8  # I列索引为8 (0-based)
                if idx < len(df_source) and attribute_col < len(df_source.columns):
                    attribute_value = str(df_source.iloc[idx, attribute_col]).strip() if not pd.isna(df_source.iloc[idx, attribute_col]) else ""
                    self.log(f"检查第{excel_row}行，I列值: '{attribute_value}'")
                    
                    # 检查J列是否为空
                    j_col = 9  # J列索引为9 (0-based)
                    j_value = df_source.iloc[idx, j_col] if j_col < len(df_source.columns) else None
                    j_is_empty = pd.isna(j_value) or str(j_value).strip() == ""
                    self.log(f"  第{excel_row}行，J列值: '{j_value}', 是否为空: {j_is_empty}")
                    
                    if attribute_value == "外购件" and j_is_empty:
                        empty_j_parts_rows.append(excel_row)
                        self.log(f"找到J列为空的外购件，位于第{excel_row}行")
            
            if not empty_j_parts_rows:
                self.log("未发现J列为空的外购件")
                return []
            
            self.log(f"总共找到{len(empty_j_parts_rows)}个J列为空的外购件，位于以下行: {empty_j_parts_rows}")
            
            # 使用传入的工作簿C的对象
            third_ws = third_wb[third_sheet_name]
            self.log(f"已使用工作簿C的工作表: {third_sheet_name}")
            
            # 使用xlwings获取工作簿B中的特定单元格值
            app = xw.App(visible=False)
            try:
                wb_xw = app.books.open(target_file, update_links=False, read_only=True)
                self.log(f"使用xlwings打开工作簿B: {target_file}(只读模式)")
                
                ws_xw_bb = wb_xw.sheets[1]  # 第二个工作表 (BB)
                
                # 获取BB-AI2和BB-AK2的值
                bb_ai2_value = ws_xw_bb.range('AI2').value  # 用于A列
                bb_ak2_value = ws_xw_bb.range('AK2').value  # 用于J列
                
                self.log(f"BB-AI2单元格: {bb_ai2_value}")
                self.log(f"BB-AK2单元格: {bb_ak2_value}")
                
                # 处理每个J列为空的外购件
                processed_count = 0
                
                # 获取搜索模式的文本描述
                search_mode_text = "模糊" if self.search_mode == 1 else "精确"

                for i, row_num in enumerate(empty_j_parts_rows):
                    # 计算外购件在工作簿C中的行号
                    cb_row = row_num + 8
                    
                    self.log(f"开始处理第{i+1}/{len(empty_j_parts_rows)}个J列为空的外购件 (行{row_num} -> CB行{cb_row})")
                    
                    # 根据行号计算源工作表中的索引位置
                    source_idx = row_num - 2
                    
                    # 获取B、C、D列的值
                    b_value = df_source.iloc[source_idx, 1] if not pd.isna(df_source.iloc[source_idx, 1]) else ""  # B列索引为1 (0-based)
                    c_value = df_source.iloc[source_idx, 2] if not pd.isna(df_source.iloc[source_idx, 2]) else ""  # C列索引为2 (0-based)
                    d_value = df_source.iloc[source_idx, 3] if not pd.isna(df_source.iloc[source_idx, 3]) else ""  # D列索引为3 (0-based)
                    
                    self.log(f"  B列值: '{b_value}'")
                    self.log(f"  C列值: '{c_value}'")
                    self.log(f"  D列值: '{d_value}'")
                    
                    # 在外购件库中查找匹配项
                    matched_items = self.find_matches_in_db(b_value, c_value, d_value, self.excel_files)
                    
                    selected_item = None
                    if matched_items:
                        # 即使只有一个匹配项，也让用户选择
                        source_data = {
                            'row_num': row_num,
                            'search_mode': search_mode_text,
                            'part_b': b_value,
                            'part_c': c_value
                        }
                        selected_index = self.show_selection_dialog(matched_items, source_data)
                        
                        if selected_index is not None:
                            selected_item = matched_items[selected_index]
                            self.log(f"  用户选择了第{selected_index+1}个匹配项: {selected_item['file']}, 工作表: {selected_item['sheet']}, 行: {selected_item['row']}")
                        else:
                            self.log(f"  用户未选择匹配项，跳过该外购件")
                            # 只在用户未选择匹配项时添加到未匹配列表
                            not_found_info = {
                                'row': row_num,
                                'b_value': b_value,
                                'c_value': c_value,
                                'd_value': d_value
                            }
                            self.not_found_items.append(not_found_info)
                    else:
                        self.log(f"  未找到任何匹配项")
                        # 未找到匹配项时添加到未匹配列表
                        not_found_info = {
                            'row': row_num,
                            'b_value': b_value,
                            'c_value': c_value,
                            'd_value': d_value
                        }
                        self.not_found_items.append(not_found_info)
                    
                    if selected_item:
                        # 第一步: 映射外购件库中的C列值到CB-K列(合并单元格)
                        third_ws.cell(row=cb_row, column=11).value = selected_item['c_value']
                        third_ws.cell(row=cb_row, column=11).font = Font(name='仿宋', size=10)
                        
                        # 合并CB-K到CB-Q
                        # 先检查这些单元格是否已经被合并
                        is_merged = False
                        for merged_range in third_ws.merged_cells.ranges:
                            min_row, min_col, max_row, max_col = merged_range.min_row, merged_range.min_col, merged_range.max_row, merged_range.max_col
                            if min_row <= cb_row <= max_row and 11 <= min_col and max_col <= 17:
                                is_merged = True
                                break
                                
                        if is_merged:
                            self.log(f"  行{cb_row}的K-Q列已经合并，将直接更新值")
                        else:
                            try:
                                third_ws.merge_cells(start_row=cb_row, start_column=11, end_row=cb_row, end_column=17)
                                self.log(f"  已合并 CB-K{cb_row}:Q{cb_row} 单元格")
                            except Exception as e:
                                self.log(f"  合并 CB-K{cb_row}:Q{cb_row} 单元格时出错: {str(e)}")
                        
                        self.log(f"  映射 XX-C{selected_item['row']}({selected_item['c_value']}) -> CB-K{cb_row}")
                        
                        # 第二步: 映射外购件库中的B列值(SAP号)到CB-H
                        third_ws.cell(row=cb_row, column=8).value = selected_item['b_value']
                        third_ws.cell(row=cb_row, column=8).font = Font(name='仿宋', size=10)
                        self.log(f"  映射 XX-B{selected_item['row']}({selected_item['b_value']}) -> CB-H{cb_row}")
                    else:
                        self.log(f"  未在外购件库中找到匹配项，或用户未选择匹配项")
                    
                    # 第三步: 映射F、E、G列到目标工作表
                    # F列（数量）-> CB-I
                    f_value = df_source.iloc[source_idx, 5]  # F列索引为5 (0-based)
                    third_ws.cell(row=cb_row, column=9).value = f_value if not pd.isna(f_value) else ""
                    third_ws.cell(row=cb_row, column=9).font = Font(name='仿宋', size=10)
                    self.log(f"  映射 AA-F{row_num}({f_value}) -> CB-I{cb_row}")
                    
                    # E列（材料）-> CB-R
                    # 修改规则：如果E列为空，则R列填入"组件"
                    e_value = df_source.iloc[source_idx, 4]  # E列索引为4 (0-based)
                    if pd.isna(e_value) or str(e_value).strip() == "":
                        r_value = "组件"
                        self.log(f"  AA-E{row_num}为空，CB-R{cb_row}填入'组件'")
                    else:
                        r_value = e_value
                    third_ws.cell(row=cb_row, column=18).value = r_value
                    third_ws.cell(row=cb_row, column=18).font = Font(name='仿宋', size=10)
                    third_ws.cell(row=cb_row, column=18).alignment = Alignment(horizontal='center', vertical='center')
                    self.log(f"  映射 AA-E{row_num}({e_value}) -> CB-R{cb_row}({r_value})")
                    
                    # G列（重量）-> CB-S
                    g_value = df_source.iloc[source_idx, 6]  # G列索引为6 (0-based)
                    third_ws.cell(row=cb_row, column=19).value = g_value if not pd.isna(g_value) else ""
                    third_ws.cell(row=cb_row, column=19).font = Font(name='仿宋', size=10)
                    third_ws.cell(row=cb_row, column=19).alignment = Alignment(horizontal='center', vertical='center')
                    self.log(f"  映射 AA-G{row_num}({g_value}) -> CB-S{cb_row}")
                    
                    # 第四步: 映射BB-AI2和BB-AK2到目标工作表
                    # BB-AI2 -> CB-A
                    third_ws.cell(row=cb_row, column=1).value = bb_ai2_value
                    third_ws.cell(row=cb_row, column=1).font = Font(name='仿宋', size=10)
                    self.log(f"  映射 BB-AI2({bb_ai2_value}) -> CB-A{cb_row}")
                    
                    # BB-AK2 -> CB-J
                    third_ws.cell(row=cb_row, column=10).value = bb_ak2_value
                    third_ws.cell(row=cb_row, column=10).font = Font(name='仿宋', size=10)
                    self.log(f"  映射 BB-AK2({bb_ak2_value}) -> CB-J{cb_row}")
                    
                    # 设置所有单元格的虚线边框
                    dashed_side = Side(style='dashed')
                    dashed_border = Border(
                        left=dashed_side,
                        right=dashed_side,
                        top=dashed_side,
                        bottom=dashed_side
                    )
                    
                    # 应用边框到所有单元格
                    for col in range(1, 21):  # A到T列
                        cell = third_ws.cell(row=cb_row, column=col)
                        cell.border = dashed_border
                    
                    # 设置行高为20
                    third_ws.row_dimensions[cb_row].height = 20
                    
                    processed_count += 1
                    self.log(f"  完成第{i+1}个外购件的处理")
            finally:
                # 关闭xlwings
                try:
                    wb_xw.close()
                    app.quit()
                    self.log("已关闭xlwings")
                except:
                    pass
            
            # 保存工作簿C之前，填入固定值
            self.fill_fixed_values(third_ws)
            
            # 保存工作簿C
            try:
                # 不再使用self.third_file，因为这个变量不存在
                # 在新的构造函数中，我们没有存储文件路径
                # third_wb.save(self.third_file)
                # self.log(f"已保存工作簿C到: {self.third_file}")
                
                # 注意：我们不需要在这里保存工作簿C
                # 工作簿C将由调用者(bom_mapping.py)在所有处理完成后统一保存
                self.log(f"工作簿C的修改已完成，将由主程序统一保存")
            except Exception as e:
                self.log(f"处理工作簿C时出错: {str(e)}")
            
            # 显示未找到匹配的外购件总结
            if self.not_found_items:
                root = tk.Tk()
                root.withdraw()
                
                # 构建消息内容
                message = f"以下{len(self.not_found_items)}个J列为空的外购件未找到匹配:\n\n"
                for i, item in enumerate(self.not_found_items):  # 显示所有未匹配项
                    message += f"{i+1}. 行{item['row']}: 名称={item['c_value']}, 型号={item['d_value']}\n"
                
                messagebox.showwarning("未找到匹配的J列为空外购件汇总", message)
                root.destroy()
            
            self.log(f"J列为空的外购件处理完成，共找到{len(empty_j_parts_rows)}个外购件，成功处理{processed_count}个，未找到匹配{len(self.not_found_items)}个")
            return self.not_found_items
            
        except Exception as e:
            self.log(f"处理J列为空的外购件时出错: {str(e)}")
            return []

    def load_purchased_parts_db(self):
        """加载外购件库中的所有Excel文件"""
        self.log("加载外购件库...")
        
        if not self.purchased_parts_dir or not os.path.exists(self.purchased_parts_dir):
            self.log("警告: 外购件库路径不存在")
            return []
        
        # 获取外购件库文件夹中的所有Excel文件
        excel_files = glob.glob(os.path.join(self.purchased_parts_dir, "*.xlsx"))
        excel_files.extend(glob.glob(os.path.join(self.purchased_parts_dir, "*.xls")))
        
        self.log(f"在外购件库中找到{len(excel_files)}个Excel文件")
        return excel_files

    def show_selection_dialog(self, matched_items, source_data):
        """
        显示经过美化的选择对话框，样式基于'弹窗html-4.html'
        
        Args:
            matched_items (list): 匹配项列表
            source_data (dict): 源数据信息，包含 'row_num', 'search_mode', 'part_b', 'part_c'
            
        Returns:
            int or None: 选择的索引，如果取消则返回None
        """
        
        # --- 颜色和字体定义 ---
        colors = {
            'accent': '#38b2ac',
            'accent_light': '#e6fffa',
            'bg_dialog': '#ffffff',
            'bg_panel': '#f7fafc',
            'bg_hover': '#edf2f7',
            'text_dark': '#1a202c',
            'text_medium': '#4a5568',
            'text_light': '#718096',
            'border': '#e2e8f0',
            'white': '#ffffff'
        }
        
        fonts = {
            'body': ("Microsoft YaHei UI", 9),
            'body_bold': ("Microsoft YaHei UI", 9, "bold"),
            'h1': ("Microsoft YaHei UI", 14, "bold"),
            'h3': ("Microsoft YaHei UI", 10, "bold"),
        }

        # --- 对话框初始化 ---
        root = tk._default_root or tk.Tk()
        if not tk._default_root:
             root.withdraw()

        dialog = Toplevel(root)
        dialog.title("选择匹配项")
        dialog.configure(bg=colors['bg_dialog'])
        dialog.resizable(False, False)
        dialog.transient(root)
        dialog.grab_set()

        # 计算居中位置
        dialog.update_idletasks()
        width = 900
        height = 600
        x = root.winfo_x() + (root.winfo_width() // 2) - (width // 2)
        y = root.winfo_y() + (root.winfo_height() // 2) - (height // 2)
        dialog.geometry(f'{width}x{height}+{x}+{y}')

        result = [None] # 使用列表来存储选择结果
        
        # --- 布局框架 ---
        header_frame = Frame(dialog, bg=colors['bg_dialog'], padx=24, pady=20)
        separator = Frame(dialog, height=1, bg=colors['border'])
        body_frame = Frame(dialog, bg=colors['bg_dialog'])
        footer_frame = Frame(dialog, bg=colors['bg_panel'], padx=24, pady=12)

        header_frame.pack(fill='x')
        separator.pack(fill='x')
        body_frame.pack(fill='both', expand=True)
        footer_frame.pack(fill='x')

        # --- Header ---
        title_label = Label(header_frame, text="选择外购件匹配项", fg=colors['text_dark'], bg=colors['bg_dialog'], font=fonts['h1'])
        title_label.pack(anchor='w')
        
        subtitle_label = Label(header_frame, text="为确保BOM表数据的准确性，请从下方列表中选择一个最精确的匹配项。", 
                               fg=colors['text_light'], bg=colors['bg_dialog'], font=fonts['body'])
        subtitle_label.pack(anchor='w', pady=(4, 0))

        # --- Body ---
        left_panel = Frame(body_frame, bg=colors['bg_panel'], width=320, padx=24, pady=24)
        body_separator = Frame(body_frame, width=1, bg=colors['border'])
        right_panel = Frame(body_frame, bg=colors['bg_dialog'], padx=24, pady=24)

        left_panel.pack(side='left', fill='y')
        body_separator.pack(side='left', fill='y')
        right_panel.pack(side='left', fill='both', expand=True)
        left_panel.pack_propagate(False) # 防止改变大小

        # --- Left Panel Content ---
        Label(left_panel, text="源数据信息", fg=colors['text_dark'], bg=colors['bg_panel'], font=fonts['h3']).pack(anchor='w', pady=(0, 24))
        
        info_frame = Frame(left_panel, bg=colors['bg_panel'])
        info_frame.pack(fill='x')
        info_data = {
            "行号:": source_data['row_num'],
            "搜索模式:": source_data['search_mode'],
            "物料编码:": source_data['part_b'],
            "物料名称:": source_data['part_c'],
        }

        for i, (label_text, value_text) in enumerate(info_data.items()):
            Label(info_frame, text=label_text, fg=colors['text_light'], bg=colors['bg_panel'], font=fonts['body_bold']).grid(row=i, column=0, sticky='e', pady=4, padx=4)
            value_label = Label(info_frame, text=value_text, fg=colors['text_dark'], bg=colors['bg_hover'], font=fonts['body'], wraplength=180, justify='left')
            value_label.grid(row=i, column=1, sticky='w', pady=4, padx=4)
            value_label.config(padx=8, pady=4)


        # --- Right Panel Content ---
        canvas = Canvas(right_panel, bg=colors['bg_dialog'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(right_panel, orient="vertical", command=canvas.yview)
        list_frame = Frame(canvas, bg=colors['bg_dialog'], padx=10)

        canvas.configure(yscrollcommand=scrollbar.set)
        
        scrollbar.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)
        canvas_frame = canvas.create_window((0, 0), window=list_frame, anchor="nw")

        def on_frame_configure(event):
            canvas.configure(scrollregion=canvas.bbox("all"))

        list_frame.bind("<Configure>", on_frame_configure)
        canvas.bind('<Enter>', lambda e: canvas.focus_set())
        
        def on_mouse_wheel(event):
             canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", on_mouse_wheel)

        match_item_widgets = []
        selected_index_var = tk.IntVar(value=-1)
        
        def select_item(index):
            selected_index_var.set(index)
            for i, item_widgets in enumerate(match_item_widgets):
                is_selected = (i == index)
                item_widgets['frame'].config(bg=colors['accent_light'] if is_selected else colors['bg_dialog'],
                                             relief='solid' if is_selected else 'flat',
                                             borderwidth=1 if is_selected else 0,
                                             highlightbackground=colors['accent'] if is_selected else colors['border'],
                                             highlightcolor=colors['accent'] if is_selected else colors['border'],
                                             highlightthickness=1)
                item_widgets['index_label'].config(bg=colors['accent'] if is_selected else colors['border'],
                                                   fg=colors['white'] if is_selected else colors['text_medium'])
                item_widgets['primary_label'].config(bg=colors['accent_light'] if is_selected else colors['bg_dialog'])


        def create_item_hover_bindings(widgets):
            frame = widgets['frame']
            
            def on_enter(e):
                is_selected = (widgets['index'] == selected_index_var.get())
                if not is_selected:
                    frame.config(bg=colors['bg_hover'])
                    widgets['primary_label'].config(bg=colors['bg_hover'])

            def on_leave(e):
                is_selected = (widgets['index'] == selected_index_var.get())
                bg_color = colors['accent_light'] if is_selected else colors['bg_dialog']
                frame.config(bg=bg_color)
                widgets['primary_label'].config(bg=bg_color)
            
            return on_enter, on_leave


        for i, item in enumerate(matched_items):
            item_frame = Frame(list_frame, bg=colors['bg_dialog'], padx=16, pady=16, 
                               highlightbackground=colors['border'], highlightthickness=1)
            item_frame.pack(fill='x', pady=(0, 8))
            
            index_label = Label(item_frame, text=str(i + 1), font=fonts['body_bold'], width=3, 
                                bg=colors['border'], fg=colors['text_medium'])
            index_label.pack(side='left', anchor='n')

            content_frame = Frame(item_frame, bg=colors['bg_dialog'])
            content_frame.pack(side='left', fill='x', expand=True, padx=(16, 0))
            
            primary_text = f"{item.get('b_value', '')}////{item.get('c_value', '')}"
            primary_label = Label(content_frame, text=primary_text, font=fonts['body_bold'], wraplength=450,
                                  justify='left', fg=colors['text_dark'], bg=colors['bg_dialog'])
            primary_label.pack(anchor='w')
            
            item_widgets = {
                'frame': item_frame, 'index_label': index_label, 'primary_label': primary_label, 
                'index': i
            }
            
            on_enter, on_leave = create_item_hover_bindings(item_widgets)

            for widget in [item_frame, index_label, content_frame, primary_label]:
                widget.bind("<Button-1>", lambda e, idx=i: select_item(idx))
                widget.bind("<Enter>", on_enter)
                widget.bind("<Leave>", on_leave)
                widget.bind("<Double-Button-1>", lambda e: on_ok())

            match_item_widgets.append(item_widgets)
        
        # 默认选中第一项
        if match_item_widgets:
            select_item(0)

        # --- Footer Buttons ---
        def on_ok():
            selected_index = selected_index_var.get()
            if selected_index != -1:
                result[0] = selected_index
                dialog.destroy()
            else:
                messagebox.showwarning("未选择", "请选择一个匹配项。")

        def on_cancel():
            result[0] = None
            dialog.destroy()

        dialog.protocol("WM_DELETE_WINDOW", on_cancel)

        cancel_button = Button(footer_frame, text="取消", command=on_cancel, font=fonts['body_bold'],
                               bg=colors['white'], fg=colors['text_medium'], relief='solid', borderwidth=1,
                               padx=20, pady=5)
        cancel_button.pack(side='right', padx=(10, 0))

        ok_button = Button(footer_frame, text="确认选择", command=on_ok, font=fonts['body_bold'],
                           bg=colors['accent'], fg=colors['white'], relief='flat', borderwidth=0,
                           padx=20, pady=5)
        ok_button.pack(side='right')

        dialog.wait_window()
        
        # 移除事件绑定避免影响主程序
        try:
            canvas.unbind_all("<MouseWheel>")
        except tk.TclError:
            pass # 可能窗口已销毁
        
        return result[0]

    def find_matches_in_db(self, b_value, c_value, d_value, db_files):
        """
        根据搜索模式在外购件库中查找匹配项
        
        Args:
            b_value: B列值(项目号)
            c_value: C列值(名称)
            d_value: D列值(型号/规格)
            db_files: 外购件库文件列表
            
        Returns:
            list: 匹配项列表
        """
        matched_items = []
        search_mode = self.search_mode
        
        # 安全检查：确保所有输入值都是有效的
        if not db_files:
            self.log("  错误: 未提供外购件库文件列表")
            return []
        
        # 处理输入值，确保它们是字符串类型
        try:
            b_value = str(b_value).strip() if b_value is not None and not pd.isna(b_value) else ""
            c_value = str(c_value).strip() if c_value is not None and not pd.isna(c_value) else ""
            d_value = str(d_value).strip() if d_value is not None and not pd.isna(d_value) else ""
        except Exception as e:
            self.log(f"  警告: 处理输入值时出错: {str(e)}")
            b_value = str(b_value) if b_value is not None else ""
            c_value = str(c_value) if c_value is not None else ""
            d_value = str(d_value) if d_value is not None else ""
        
        self.log(f"  处理后的B列值: '{b_value}'")
        self.log(f"  处理后的C列值: '{c_value}'")
        self.log(f"  处理后的D列值: '{d_value}'")
        
        # 确定要搜索的文件
        target_db_files = db_files
        
        # 基于关键词选择特定文件
        if b_value:
            if "软管" in b_value:
                # 查找软管相关文件
                rg_files = [f for f in db_files if "软管新增登记表" in os.path.basename(f)]
                if rg_files:
                    target_db_files = rg_files
                    self.log(f"  B列包含'软管'关键词，将在{len(rg_files)}个软管相关文件中搜索")
                else:
                    self.log(f"  B列包含'软管'关键词，但未找到相关文件")
            elif "阀块" in b_value:
                # 查找阀块相关文件
                fb_files = [f for f in db_files if "阀块新增登记表" in os.path.basename(f)]
                if fb_files:
                    target_db_files = fb_files
                    self.log(f"  B列包含'阀块'关键词，将在{len(fb_files)}个阀块相关文件中搜索")
                else:
                    self.log(f"  B列包含'阀块'关键词，但未找到相关文件")
        
        # 模式1: 模糊搜索 - 将B、C、D列值组合后搜索
        if search_mode == 1:
            # 组合搜索文本并预处理
            original_search_text = f"{b_value} {c_value} {d_value}".strip()
            
            # 如果原始搜索文本为空，尝试使用单个非空值
            if not original_search_text:
                if b_value:
                    original_search_text = b_value
                elif c_value:
                    original_search_text = c_value
                elif d_value:
                    original_search_text = d_value
                else:
                    self.log(f"  错误: B、C、D列均为空，无法进行搜索")
                    return []
            
            try:
                search_text = ''.join(char for char in original_search_text if char.isalnum() or '\u4e00' <= char <= '\u9fff')
            except Exception as e:
                self.log(f"  警告: 处理搜索文本时出错: {str(e)}")
                search_text = original_search_text
            
            self.log(f"  模糊搜索模式, 原始组合文本: '{original_search_text}'")
            self.log(f"  处理后的搜索文本: '{search_text}'")
            
            if not search_text:
                self.log(f"  处理后的搜索文本为空，无法搜索")
                return []
            
            # 搜索逻辑
            for db_file in target_db_files:
                try:
                    self.log(f"  搜索文件: {os.path.basename(db_file)}")
                    
                    # 检查文件是否存在
                    if not os.path.exists(db_file):
                        self.log(f"  错误: 文件不存在: {db_file}")
                        continue
                    
                    # 检查文件是否可读
                    try:
                        with open(db_file, 'rb') as f:
                            pass
                    except Exception as e:
                        self.log(f"  错误: 无法打开文件: {db_file}, 原因: {str(e)}")
                        continue
                    
                    try:
                        db_wb = load_workbook(db_file, read_only=True)
                    except Exception as e:
                        self.log(f"  错误: 无法加载工作簿: {db_file}, 原因: {str(e)}")
                        continue
                    
                    if not db_wb.sheetnames:
                        self.log(f"  警告: 工作簿没有工作表: {db_file}")
                        continue
                    
                    for sheet_name in db_wb.sheetnames:
                        try:
                            self.log(f"    检查工作表: {sheet_name}")
                            df_db = pd.read_excel(db_file, sheet_name=sheet_name)
                            
                            if df_db.empty:
                                self.log(f"    警告: 工作表为空: {sheet_name}")
                                continue
                                
                            # 在C列中查找包含搜索文本字符的单元格
                            for db_idx, db_row in df_db.iterrows():
                                try:
                                    # 确保C列存在并且有值
                                    if len(df_db.columns) <= 2:
                                        continue
                                        
                                    db_c_value = df_db.iloc[db_idx, 2]  # C列索引为2 (0-based)
                                    if pd.isna(db_c_value):
                                        continue
                                        
                                    db_c_value = str(db_c_value).strip()
                                    
                                    # 处理外购件库中的C列值，去除空格和特殊字符
                                    try:
                                        db_c_processed = ''.join(char for char in db_c_value if char.isalnum() or '\u4e00' <= char <= '\u9fff')
                                    except Exception as e:
                                        self.log(f"    警告: 处理C列值时出错: {str(e)}")
                                        db_c_processed = db_c_value
                                    
                                    # 计算匹配度 - 检查搜索文本中每个字符在C列处理后的值中是否存在
                                    total_chars = len(search_text)
                                    matched_chars = sum(1 for char in search_text if char.lower() in db_c_processed.lower())
                                    
                                    # 计算匹配百分比
                                    if total_chars > 0:
                                        match_percentage = (matched_chars / total_chars) * 100
                                    else:
                                        match_percentage = 0
                                        
                                    # 匹配度达到50%以上才算匹配
                                    if match_percentage >= 50:
                                        # 获取B列值作为SAP号
                                        db_b_value = ""
                                        try:
                                            if len(df_db.columns) > 1:
                                                db_b_value = str(df_db.iloc[db_idx, 1]).strip() if not pd.isna(df_db.iloc[db_idx, 1]) else ""
                                        except Exception as e:
                                            self.log(f"    警告: 获取B列值时出错: {str(e)}")
                                        
                                        matched_items.append({
                                            'file': os.path.basename(db_file),
                                            'sheet': sheet_name,
                                            'row': db_idx + 2,
                                            'c_value': db_c_value,
                                            'b_value': db_b_value,
                                            'match_score': match_percentage
                                        })
                                        self.log(f"    找到可能匹配: 行: {db_idx+2}, C列值: '{db_c_value}', 匹配度: {match_percentage:.2f}%")
                                except Exception as e:
                                    self.log(f"    警告: 处理行 {db_idx+2} 时出错: {str(e)}")
                        except Exception as e:
                            self.log(f"    错误: 读取工作表 {sheet_name} 时出错: {str(e)}")
                except Exception as e:
                    self.log(f"  错误: 处理文件 {os.path.basename(db_file)} 时出错: {str(e)}")
        
        # 模式2: 精确搜索 - B、C、D列值分别匹配
        elif search_mode == 2:
            self.log(f"  精确搜索模式")
            
            # 将B、C、D列的值分别作为关键词
            keywords = []
            if b_value:
                keywords.append(b_value)
            if c_value:
                keywords.append(c_value)
            if d_value:
                keywords.append(d_value)
            
            self.log(f"  搜索关键词: {keywords}")
            
            if not keywords:
                self.log(f"  没有有效的搜索关键词")
                return []
            
            # 搜索逻辑
            for db_file in target_db_files:
                try:
                    self.log(f"  搜索文件: {os.path.basename(db_file)}")
                    
                    # 检查文件是否存在
                    if not os.path.exists(db_file):
                        self.log(f"  错误: 文件不存在: {db_file}")
                        continue
                    
                    try:
                        db_wb = load_workbook(db_file, read_only=True)
                    except Exception as e:
                        self.log(f"  错误: 无法加载工作簿: {db_file}, 原因: {str(e)}")
                        continue
                    
                    for sheet_name in db_wb.sheetnames:
                        try:
                            self.log(f"    检查工作表: {sheet_name}")
                            df_db = pd.read_excel(db_file, sheet_name=sheet_name)
                            
                            if df_db.empty:
                                self.log(f"    警告: 工作表为空: {sheet_name}")
                                continue
                            
                            # 在C列中查找包含所有关键词的单元格
                            for db_idx, db_row in df_db.iterrows():
                                try:
                                    # 确保C列存在并且有值
                                    if len(df_db.columns) <= 2:
                                        continue
                                        
                                    db_c_value = df_db.iloc[db_idx, 2]  # C列索引为2 (0-based)
                                    if pd.isna(db_c_value):
                                        continue
                                        
                                    db_c_value = str(db_c_value).strip()
                                    
                                    # 检查所有关键词是否都包含在C列值中
                                    all_keywords_found = True
                                    for keyword in keywords:
                                        if keyword and keyword.strip() and keyword.strip().lower() not in db_c_value.lower():
                                            all_keywords_found = False
                                            break
                                    
                                    if all_keywords_found:
                                        # 获取B列值作为SAP号
                                        db_b_value = ""
                                        try:
                                            if len(df_db.columns) > 1:
                                                db_b_value = str(df_db.iloc[db_idx, 1]).strip() if not pd.isna(df_db.iloc[db_idx, 1]) else ""
                                        except Exception as e:
                                            self.log(f"    警告: 获取B列值时出错: {str(e)}")
                                        
                                        # 计算匹配分数 - 简单起见，这里使用关键词数量作为分数
                                        match_score = 100.0  # 完全匹配给100分
                                        
                                        matched_items.append({
                                            'file': os.path.basename(db_file),
                                            'sheet': sheet_name,
                                            'row': db_idx + 2,
                                            'c_value': db_c_value,
                                            'b_value': db_b_value,
                                            'match_score': match_score,
                                            'keywords': keywords
                                        })
                                        self.log(f"    找到精确匹配: 行: {db_idx+2}, C列值: '{db_c_value}'")
                                except Exception as e:
                                    self.log(f"    警告: 处理行 {db_idx+2} 时出错: {str(e)}")
                        except Exception as e:
                            self.log(f"    错误: 读取工作表 {sheet_name} 时出错: {str(e)}")
                except Exception as e:
                    self.log(f"  错误: 处理文件 {os.path.basename(db_file)} 时出错: {str(e)}")
        
        # 根据匹配度排序
        matched_items.sort(key=lambda x: x['match_score'], reverse=True)
        
        self.log(f"  总共找到 {len(matched_items)} 个匹配项")
        return matched_items


def main():
    """测试J列为空的外购件处理器的主函数"""
    # 测试用的文件路径
    source_file = "path/to/workbook_a.xlsx"
    target_file = "path/to/workbook_b.xlsx"
    third_file = "path/to/workbook_c.xlsx"
    
    # 创建处理器
    processor = EmptyCellPartsProcessor(source_file, target_file, third_file)
    
    # 处理J列为空的外购件
    processor.process(source_file, target_file, third_file)


if __name__ == "__main__":
    main() 