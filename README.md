# 天锻重工数据处理系统

一个集成了BOM表制作工具和重型装备数据处理系统的统一界面应用。

## 功能特点

- 现代化美观的用户界面
- 丰富的动画和交互效果
- 集成了两个独立的数据处理系统
- 支持在不同系统间无缝切换
- 全新设计的BOM表制作工具界面，更加直观易用

## 界面改进

### 统一启动界面
- 深色主题设计，具有粒子背景动画效果
- 流畅的文本和按钮动画
- 点击按钮时的波纹效果
- 平滑的页面过渡动画

### BOM表制作工具界面
- 全新的布局设计，更加清晰直观
- 左右分栏布局，提高工作效率
- 文件选择和操作设置区域分离
- 实时日志显示区域
- 现代化的按钮和表单设计
- 更好的错误处理和用户提示

## 系统要求

- Python 3.6+
- 以下Python库:
  - tkinter (通常随Python一起安装)
  - PIL/Pillow (`pip install pillow`)
  - pandas (`pip install pandas`)
  - openpyxl (`pip install openpyxl`)
  - xlwings (`pip install xlwings`)
  - xlrd (`pip install xlrd`)
  - ezdxf (`pip install ezdxf`)

## 安装步骤

1. 确保已安装Python 3.6或更高版本
2. 运行启动脚本，它会自动检查并安装所需的依赖库:

```bash
python run.py
```

或者手动安装依赖:

```bash
pip install pillow pandas openpyxl xlwings xlrd ezdxf
```

3. 如果手动安装依赖，请运行图片生成脚本创建必要的图像资源:

```bash
python create_images.py
```

4. 然后运行主程序:

```bash
python unified_app.py
```

## 使用说明

### 启动界面

启动程序后，会显示一个美观的启动界面，提供两个选项:

- **BOM表制作工具**: 用于BOM表的制作和处理
- **重型装备数据处理系统**: 用于处理密封明细、油箱明细表等数据

点击任意按钮进入相应的系统。

### BOM表制作工具

BOM表制作工具提供以下功能:

1. 在不同工作簿之间映射数据
2. 处理自制件和外购件
3. 支持三种操作模式:
   - 模式1: A→B映射（生成自制件和工作簿B的工作表D的数据）
   - 模式2: B→C映射（将工作簿B的数据映射到工作簿C）
   - 模式3: 执行全部映射（完成所有映射操作）

### 重型装备数据处理系统

重型装备数据处理系统提供以下功能:

1. 处理密封明细统计
2. 处理油箱明细表/标准件表/BOM表
3. DWG文件处理和DXF输出
4. 提取零件信息和密封规格统计

## 注意事项

- 使用重型装备数据处理系统前，请先安装ODA FileConverter软件到"D:\software\ODA"
- 处理前会自动清空"密封明细统计"工作簿数据区域，请注意备份重要数据
- 支持的文件格式：Excel (.xlsx、.xlsm、.xls)和图纸 (.dwg)

## 版权信息

© 2025 天锻重工

## 联系方式

如有问题或改进建议请联系重型装备设计所。 