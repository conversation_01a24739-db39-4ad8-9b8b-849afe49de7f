#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试main.py的启动脚本
"""

import sys
import os
import traceback

def test_main():
    """测试main.py的启动"""
    try:
        print("正在启动重型装备数据处理系统...")
        print("检查依赖模块...")
        
        # 检查必要的模块
        required_modules = ['tkinter', 'PIL', 'openpyxl', 'pandas']
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
                print(f"✓ {module} 模块可用")
            except ImportError:
                missing_modules.append(module)
                print(f"✗ {module} 模块缺失")
        
        if missing_modules:
            print(f"\n缺失模块: {', '.join(missing_modules)}")
            print("请安装缺失的模块后重试")
            return False
        
        print("\n所有依赖模块检查完成，启动主程序...")
        
        # 导入并启动主程序
        from main import ExcelToolApp
        import tkinter as tk
        
        root = tk.Tk()
        app = ExcelToolApp(root)
        
        print("界面创建成功，启动主循环...")
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"\n启动失败: {str(e)}")
        print("\n详细错误信息:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_main()
    if not success:
        input("\n按回车键退出...")
