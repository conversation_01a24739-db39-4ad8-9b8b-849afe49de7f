<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重型装备数据处理系统 - 现代化界面预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei UI', sans-serif;
            background-color: #FAFAFA;
            color: #212121;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(135deg, #1976D2, #42A5F5);
            color: white;
            padding: 15px 30px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .logo {
            width: 50px;
            height: 50px;
            background: white;
            color: #1976D2;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
            margin-right: 15px;
        }

        .company-info h1 {
            font-size: 18px;
            margin-bottom: 2px;
        }

        .company-info p {
            font-size: 10px;
            opacity: 0.9;
        }

        .header-right h2 {
            font-size: 20px;
            text-align: right;
        }

        .header-right .version {
            font-size: 10px;
            opacity: 0.8;
        }

        /* 主内容区域 */
        .main-container {
            display: flex;
            padding: 20px;
            gap: 15px;
            min-height: calc(100vh - 140px);
        }

        /* 侧边栏 */
        .sidebar {
            width: 350px;
            background: white;
            border: 1px solid #E0E0E0;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .machine-icon {
            font-size: 48px;
            text-align: center;
            margin-bottom: 20px;
        }

        .company-card {
            background: linear-gradient(135deg, #1976D2, #42A5F5);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 20px;
        }

        .help-section h3 {
            color: #1976D2;
            font-size: 14px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .help-content {
            background: #F8F9FA;
            padding: 15px;
            border-radius: 6px;
            font-size: 9px;
            line-height: 1.6;
            max-height: 300px;
            overflow-y: auto;
        }

        /* 主内容 */
        .content {
            flex: 1;
            padding-left: 10px;
        }

        .section {
            background: white;
            border: 1px solid #E0E0E0;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .section-header {
            padding: 15px 20px;
            border-bottom: 1px solid #E0E0E0;
            font-weight: bold;
            color: #1976D2;
            font-size: 12px;
        }

        .section-content {
            padding: 20px;
        }

        /* 文件选择网格 */
        .file-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .file-item {
            border: 1px solid #E0E0E0;
            border-radius: 6px;
            padding: 15px;
            background: #FAFAFA;
        }

        .file-item-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .file-icon {
            font-size: 16px;
            margin-right: 8px;
            color: #1976D2;
        }

        .file-title {
            font-weight: bold;
            font-size: 11px;
        }

        .file-status {
            font-size: 9px;
            color: #757575;
            margin-bottom: 8px;
        }

        .file-button {
            background: #1976D2;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            font-size: 10px;
            cursor: pointer;
            width: 100%;
            transition: background 0.3s;
        }

        .file-button:hover {
            background: #42A5F5;
        }

        /* 参数配置 */
        .config-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .config-section h4 {
            color: #1976D2;
            font-size: 12px;
            margin-bottom: 10px;
        }

        .radio-group {
            margin-bottom: 10px;
        }

        .radio-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 10px;
        }

        .radio-item input {
            margin-right: 8px;
        }

        .input-group label {
            display: block;
            font-size: 9px;
            color: #757575;
            margin-bottom: 5px;
        }

        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #E0E0E0;
            border-radius: 4px;
            font-size: 10px;
        }

        /* 按钮区域 */
        .button-section {
            display: flex;
            flex-direction: column;
            gap: 15px;
            align-items: center;
        }

        .main-button-card {
            background: white;
            border: 1px solid #E0E0E0;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .button-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .main-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .main-button:hover {
            background: #66BB6A;
        }

        .secondary-button {
            background: #FF5722;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .secondary-button:hover {
            background: #FF7043;
        }

        /* 状态栏 */
        .status-bar {
            background: white;
            border-top: 1px solid #E0E0E0;
            padding: 8px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 10px;
            color: #757575;
        }

        .status-left, .status-right {
            display: flex;
            align-items: center;
        }

        .status-icon {
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <div class="header">
        <div class="header-left">
            <div class="logo">天锻</div>
            <div class="company-info">
                <h1>天锻重工</h1>
                <p>专注于重型装备制造</p>
            </div>
        </div>
        <div class="header-right">
            <h2>重型装备数据处理系统</h2>
            <div class="version">Version 1.0</div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="machine-icon">🏭</div>
            
            <div class="company-card">
                <div style="font-weight: bold; margin-bottom: 5px;">天锻重工</div>
                <div style="font-size: 12px;">专注于重型装备制造<br>致力于技术创新</div>
            </div>

            <div class="help-section">
                <h3>📖 使用说明</h3>
                <div class="help-content">
                    <strong>1. 选择文件</strong><br>
                    • 数据储存：选择"密封明细统计"文件<br>
                    • 油箱明细表/标准件表/BOM表：可选择多个Excel文件<br>
                    • DWG文件：选择DWG文件夹和DXF输出文件夹<br><br>

                    <strong>2. 配置参数</strong><br>
                    • 选择关键词来源（自动读取或手动输入）<br>
                    • 根据需要调整相关设置<br><br>

                    <strong>3. 开始处理</strong><br>
                    • 点击"开始处理"按钮<br>
                    • 等待处理完成<br><br>

                    <strong>4. 查看结果</strong><br>
                    • Sheet1的A-D列：提取的零件信息<br>
                    • Sheet1的F-G列：密封规格统计结果<br>
                    • 红色背景：表示未找到对应密封规格<br><br>

                    <strong>5. 注意事项</strong><br>
                    • 使用前请安装ODAFileConverter软件<br>
                    • 支持格式：Excel(.xlsx/.xlsm/.xls)，图纸(.dwg)<br>
                    • 处理前会清空数据区域，请注意备份<br><br>

                    <strong>如有问题请联系：重型装备设计所-胡猛超</strong>
                </div>
            </div>
        </div>

        <!-- 主内容 -->
        <div class="content">
            <!-- 文件选择 -->
            <div class="section">
                <div class="section-header">📁 文件选择</div>
                <div class="section-content">
                    <div class="file-grid">
                        <div class="file-item">
                            <div class="file-item-header">
                                <span class="file-icon">💾</span>
                                <span class="file-title">数据储存</span>
                            </div>
                            <div class="file-status">未选择</div>
                            <button class="file-button">选择文件</button>
                        </div>
                        <div class="file-item">
                            <div class="file-item-header">
                                <span class="file-icon">📊</span>
                                <span class="file-title">油箱明细表</span>
                            </div>
                            <div class="file-status">未选择</div>
                            <button class="file-button">选择文件</button>
                        </div>
                        <div class="file-item">
                            <div class="file-item-header">
                                <span class="file-icon">🔧</span>
                                <span class="file-title">标准件表</span>
                            </div>
                            <div class="file-status">未选择</div>
                            <button class="file-button">选择文件</button>
                        </div>
                        <div class="file-item">
                            <div class="file-item-header">
                                <span class="file-icon">📋</span>
                                <span class="file-title">BOM表</span>
                            </div>
                            <div class="file-status">未选择</div>
                            <button class="file-button">选择文件</button>
                        </div>
                        <div class="file-item">
                            <div class="file-item-header">
                                <span class="file-icon">📂</span>
                                <span class="file-title">DWG文件夹</span>
                            </div>
                            <div class="file-status">未选择</div>
                            <button class="file-button">选择文件夹</button>
                        </div>
                        <div class="file-item">
                            <div class="file-item-header">
                                <span class="file-icon">📤</span>
                                <span class="file-title">DXF输出文件夹</span>
                            </div>
                            <div class="file-status">未选择</div>
                            <button class="file-button">选择文件夹</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 参数配置 -->
            <div class="section">
                <div class="section-header">⚙️ 参数配置</div>
                <div class="section-content">
                    <div class="config-grid">
                        <div class="config-section">
                            <h4>📊 油箱明细表/标准件表关键词</h4>
                            <div class="radio-group">
                                <div class="radio-item">
                                    <input type="radio" name="keywords_b" value="sheet" checked>
                                    <label>从Sheet2读取</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" name="keywords_b" value="manual">
                                    <label>手动输入</label>
                                </div>
                            </div>
                            <div class="input-group">
                                <label>手动输入关键词（多个关键词用逗号分隔）</label>
                                <input type="text" placeholder="请输入关键词...">
                            </div>
                        </div>
                        <div class="config-section">
                            <h4>📐 DXF文件关键词</h4>
                            <div class="radio-group">
                                <div class="radio-item">
                                    <input type="radio" name="keywords_dxf" value="sheet" checked>
                                    <label>从Sheet2读取</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" name="keywords_dxf" value="manual">
                                    <label>手动输入</label>
                                </div>
                            </div>
                            <div class="input-group">
                                <label>手动输入关键词（多个关键词用逗号分隔）</label>
                                <input type="text" placeholder="请输入关键词...">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="button-section">
                <div class="main-button-card">
                    <div class="button-icon">🚀</div>
                    <button class="main-button">开始处理</button>
                </div>
                
                <div class="main-button-card">
                    <div class="button-icon">📋</div>
                    <button class="secondary-button">BOM表制作工具</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="status-left">
            <span class="status-icon">🕒</span>
            <span id="current-time">当前时间：2025-01-17 14:30:25</span>
        </div>
        <div class="status-right">
            <span class="status-icon">©</span>
            <span>版权所有 © 2025 天锻重工</span>
        </div>
    </div>

    <script>
        // 更新时间
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('current-time').textContent = `当前时间：${timeString}`;
        }

        // 每秒更新时间
        setInterval(updateTime, 1000);
        updateTime();

        // 添加按钮点击效果
        document.querySelectorAll('button').forEach(button => {
            button.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 100);
            });
        });
    </script>
</body>
</html>
