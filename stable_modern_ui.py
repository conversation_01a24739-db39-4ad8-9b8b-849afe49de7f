#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重型装备数据处理系统 - 现代化界面
稳定版本
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import datetime
import os
import sys

class ModernEquipmentDataSystem:
    def __init__(self, master):
        self.master = master
        self.setup_window()
        self.setup_colors()
        self.setup_variables()
        self.create_interface()
        self.update_time()

    def setup_window(self):
        """设置窗口属性"""
        self.master.title("重型装备数据处理系统")
        
        # 设置窗口大小和位置
        screen_width = self.master.winfo_screenwidth()
        screen_height = self.master.winfo_screenheight()
        window_width = int(screen_width * 0.8)
        window_height = int(screen_height * 0.8)
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        self.master.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.master.minsize(1200, 800)

    def setup_colors(self):
        """设置现代化色彩方案"""
        self.colors = {
            'primary': '#1976D2',
            'primary_light': '#42A5F5',
            'primary_dark': '#0D47A1',
            'secondary': '#FF5722',
            'accent': '#4CAF50',
            'background': '#FAFAFA',
            'surface': '#FFFFFF',
            'text_primary': '#212121',
            'text_secondary': '#757575',
            'divider': '#E0E0E0',
            'success': '#4CAF50',
            'warning': '#FF9800',
            'error': '#F44336'
        }

    def setup_variables(self):
        """初始化变量"""
        self.file_vars = {
            'data_storage': tk.StringVar(value="未选择文件"),
            'tank_details': tk.StringVar(value="未选择文件"),
            'standard_parts': tk.StringVar(value="未选择文件"),
            'bom_files': tk.StringVar(value="未选择文件"),
            'dwg_folder': tk.StringVar(value="未选择文件夹"),
            'dxf_folder': tk.StringVar(value="未选择文件夹")
        }
        
        self.keyword_source_b = tk.StringVar(value="sheet")
        self.keyword_source_dxf = tk.StringVar(value="sheet")

    def create_interface(self):
        """创建界面"""
        # 主容器
        self.main_frame = tk.Frame(self.master, bg=self.colors['background'])
        self.main_frame.pack(fill='both', expand=True)
        
        # 创建各个部分
        self.create_header()
        self.create_content_area()
        self.create_status_bar()

    def create_header(self):
        """创建头部"""
        header = tk.Frame(self.main_frame, bg=self.colors['primary'], height=80)
        header.pack(fill='x')
        header.pack_propagate(False)
        
        # 头部内容
        header_content = tk.Frame(header, bg=self.colors['primary'])
        header_content.pack(fill='both', expand=True, padx=30, pady=15)
        
        # 左侧 - Logo和公司信息
        left_section = tk.Frame(header_content, bg=self.colors['primary'])
        left_section.pack(side='left')
        
        # Logo
        logo_frame = tk.Frame(left_section, bg='white', width=50, height=50)
        logo_frame.pack(side='left', padx=(0, 15))
        logo_frame.pack_propagate(False)
        
        logo_text = tk.Label(logo_frame, text="天锻", 
                           font=('Microsoft YaHei UI', 16, 'bold'),
                           fg=self.colors['primary'], bg='white')
        logo_text.place(relx=0.5, rely=0.5, anchor='center')
        
        # 公司信息
        company_frame = tk.Frame(left_section, bg=self.colors['primary'])
        company_frame.pack(side='left')
        
        tk.Label(company_frame, text="天锻重工",
                font=('Microsoft YaHei UI', 18, 'bold'),
                fg='white', bg=self.colors['primary']).pack(anchor='w')
        
        tk.Label(company_frame, text="专注于重型装备制造",
                font=('Microsoft YaHei UI', 10),
                fg='white', bg=self.colors['primary']).pack(anchor='w')
        
        # 右侧 - 系统标题
        right_section = tk.Frame(header_content, bg=self.colors['primary'])
        right_section.pack(side='right')
        
        tk.Label(right_section, text="重型装备数据处理系统",
                font=('Microsoft YaHei UI', 20, 'bold'),
                fg='white', bg=self.colors['primary']).pack(anchor='e')
        
        tk.Label(right_section, text="Version 1.0",
                font=('Microsoft YaHei UI', 10),
                fg='white', bg=self.colors['primary']).pack(anchor='e')

    def create_content_area(self):
        """创建内容区域"""
        content_container = tk.Frame(self.main_frame, bg=self.colors['background'])
        content_container.pack(fill='both', expand=True, padx=20, pady=10)
        
        # 侧边栏
        self.create_sidebar(content_container)
        
        # 主内容
        self.create_main_content(content_container)

    def create_sidebar(self, parent):
        """创建侧边栏"""
        sidebar = tk.Frame(parent, bg=self.colors['surface'], 
                          relief='solid', bd=1, highlightbackground=self.colors['divider'])
        sidebar.pack(side='left', fill='y', padx=(0, 15))
        sidebar.configure(width=350)
        sidebar.pack_propagate(False)
        
        # 机器图标
        tk.Label(sidebar, text="🏭", font=('Microsoft YaHei UI', 48),
                bg=self.colors['surface'], fg=self.colors['primary']).pack(pady=20)
        
        # 公司信息卡片
        info_card = tk.Frame(sidebar, bg=self.colors['primary'])
        info_card.pack(fill='x', padx=20, pady=(0, 20))
        
        tk.Label(info_card, text="天锻重工\n专注于重型装备制造\n致力于技术创新",
                font=('Microsoft YaHei UI', 12, 'bold'),
                fg='white', bg=self.colors['primary'],
                justify='center', pady=15).pack()
        
        # 使用说明
        tk.Label(sidebar, text="📖 使用说明",
                font=('Microsoft YaHei UI', 14, 'bold'),
                bg=self.colors['surface'], fg=self.colors['primary']).pack(anchor='w', padx=20, pady=(0, 10))
        
        # 帮助文本
        help_frame = tk.Frame(sidebar, bg=self.colors['surface'])
        help_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))
        
        scrollbar = tk.Scrollbar(help_frame)
        scrollbar.pack(side='right', fill='y')
        
        help_text = tk.Text(help_frame, wrap=tk.WORD, font=('Microsoft YaHei UI', 9),
                           bg=self.colors['surface'], fg=self.colors['text_primary'],
                           relief='flat', bd=0, yscrollcommand=scrollbar.set)
        help_text.pack(fill='both', expand=True)
        scrollbar.config(command=help_text.yview)
        
        help_content = """1. 选择文件
• 数据储存：选择"密封明细统计"文件
• 油箱明细表/标准件表/BOM表：可选择多个Excel文件
• DWG文件：选择DWG文件夹和DXF输出文件夹

2. 配置参数
• 选择关键词来源（自动读取或手动输入）
• 根据需要调整相关设置

3. 开始处理
• 点击"开始处理"按钮
• 等待处理完成

4. 查看结果
• Sheet1的A-D列：提取的零件信息
• Sheet1的F-G列：密封规格统计结果
• 红色背景：表示未找到对应密封规格

5. 注意事项
• 使用前请安装ODAFileConverter软件
• 支持格式：Excel(.xlsx/.xlsm/.xls)，图纸(.dwg)
• 处理前会清空数据区域，请注意备份

如有问题请联系：重型装备设计所-胡猛超"""
        
        help_text.insert('1.0', help_content)
        help_text.configure(state='disabled')

    def create_main_content(self, parent):
        """创建主内容区域"""
        main_content = tk.Frame(parent, bg=self.colors['background'])
        main_content.pack(side='right', fill='both', expand=True)
        
        # 文件选择区域
        self.create_file_selection(main_content)
        
        # 参数配置区域
        self.create_parameter_config(main_content)
        
        # 操作按钮区域
        self.create_action_buttons(main_content)

    def create_file_selection(self, parent):
        """创建文件选择区域"""
        # 标题框架
        title_frame = tk.Frame(parent, bg=self.colors['surface'], 
                              relief='solid', bd=1, highlightbackground=self.colors['divider'])
        title_frame.pack(fill='x', pady=(0, 15), padx=10)
        
        tk.Label(title_frame, text="📁 文件选择",
                font=('Microsoft YaHei UI', 12, 'bold'),
                fg=self.colors['primary'], bg=self.colors['surface']).pack(anchor='w', padx=20, pady=15)
        
        # 文件选择网格
        grid_frame = tk.Frame(title_frame, bg=self.colors['surface'])
        grid_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        # 文件配置
        file_configs = [
            ("💾", "数据储存", "data_storage", self.select_data_storage),
            ("📊", "油箱明细表", "tank_details", self.select_tank_details),
            ("🔧", "标准件表", "standard_parts", self.select_standard_parts),
            ("📋", "BOM表", "bom_files", self.select_bom_files),
            ("📂", "DWG文件夹", "dwg_folder", self.select_dwg_folder),
            ("📤", "DXF输出文件夹", "dxf_folder", self.select_dxf_folder)
        ]
        
        for i, (icon, label, var_key, command) in enumerate(file_configs):
            row, col = divmod(i, 2)
            
            item_frame = tk.Frame(grid_frame, bg=self.colors['surface'], 
                                relief='solid', bd=1, highlightbackground=self.colors['divider'])
            item_frame.grid(row=row, column=col, sticky='ew', padx=5, pady=5)
            grid_frame.grid_columnconfigure(col, weight=1)
            
            # 图标和标题
            header = tk.Frame(item_frame, bg=self.colors['surface'])
            header.pack(fill='x', padx=15, pady=(10, 5))
            
            tk.Label(header, text=icon, font=('Microsoft YaHei UI', 16),
                    bg=self.colors['surface'], fg=self.colors['primary']).pack(side='left')
            
            tk.Label(header, text=label, font=('Microsoft YaHei UI', 11, 'bold'),
                    bg=self.colors['surface'], fg=self.colors['text_primary']).pack(side='left', padx=(8, 0))
            
            # 状态标签
            status_label = tk.Label(item_frame, textvariable=self.file_vars[var_key],
                                  font=('Microsoft YaHei UI', 9),
                                  bg=self.colors['surface'], fg=self.colors['text_secondary'])
            status_label.pack(fill='x', padx=15, pady=(0, 5))
            
            # 选择按钮
            button_text = "选择文件夹" if "文件夹" in label else "选择文件"
            btn = tk.Button(item_frame, text=button_text, command=command,
                           font=('Microsoft YaHei UI', 10),
                           bg=self.colors['primary'], fg='white',
                           relief='flat', bd=0, padx=15, pady=8, cursor='hand2')
            btn.pack(fill='x', padx=15, pady=(0, 10))

    def create_parameter_config(self, parent):
        """创建参数配置区域"""
        config_frame = tk.Frame(parent, bg=self.colors['surface'],
                               relief='solid', bd=1, highlightbackground=self.colors['divider'])
        config_frame.pack(fill='x', pady=(0, 15), padx=10)
        
        tk.Label(config_frame, text="⚙️ 参数配置",
                font=('Microsoft YaHei UI', 12, 'bold'),
                fg=self.colors['primary'], bg=self.colors['surface']).pack(anchor='w', padx=20, pady=(15, 10))
        
        # 两列布局
        columns_frame = tk.Frame(config_frame, bg=self.colors['surface'])
        columns_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        # 左列
        left_col = tk.Frame(columns_frame, bg=self.colors['surface'])
        left_col.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        self.create_keyword_section(left_col, "📊 油箱明细表/标准件表关键词", self.keyword_source_b)
        
        # 右列
        right_col = tk.Frame(columns_frame, bg=self.colors['surface'])
        right_col.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        self.create_keyword_section(right_col, "📐 DXF文件关键词", self.keyword_source_dxf)

    def create_keyword_section(self, parent, title, var):
        """创建关键词配置区域"""
        tk.Label(parent, text=title, font=('Microsoft YaHei UI', 12, 'bold'),
                bg=self.colors['surface'], fg=self.colors['primary']).pack(anchor='w', pady=(0, 10))
        
        # 单选按钮
        radio_frame = tk.Frame(parent, bg=self.colors['surface'])
        radio_frame.pack(fill='x', pady=(0, 10))
        
        tk.Radiobutton(radio_frame, text="从Sheet2读取", variable=var, value="sheet",
                      font=('Microsoft YaHei UI', 10),
                      bg=self.colors['surface'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['primary_light']).pack(anchor='w', pady=2)
        
        tk.Radiobutton(radio_frame, text="手动输入", variable=var, value="manual",
                      font=('Microsoft YaHei UI', 10),
                      bg=self.colors['surface'], fg=self.colors['text_primary'],
                      selectcolor=self.colors['primary_light']).pack(anchor='w', pady=2)
        
        # 输入框
        entry_frame = tk.Frame(parent, bg=self.colors['surface'])
        entry_frame.pack(fill='x', pady=(5, 0))
        
        tk.Label(entry_frame, text="手动输入关键词（多个关键词用逗号分隔）",
                font=('Microsoft YaHei UI', 9),
                bg=self.colors['surface'], fg=self.colors['text_secondary']).pack(anchor='w', pady=(0, 5))
        
        entry = tk.Entry(entry_frame, font=('Microsoft YaHei UI', 10),
                        relief='solid', bd=1, highlightthickness=1,
                        highlightcolor=self.colors['primary'])
        entry.pack(fill='x')

    def create_action_buttons(self, parent):
        """创建操作按钮区域"""
        button_container = tk.Frame(parent, bg=self.colors['background'])
        button_container.pack(fill='x', pady=20, padx=10)
        
        # 主处理按钮
        main_card = tk.Frame(button_container, bg=self.colors['surface'],
                            relief='solid', bd=1, highlightbackground=self.colors['divider'])
        main_card.pack(pady=10)
        
        main_content = tk.Frame(main_card, bg=self.colors['surface'])
        main_content.pack(padx=30, pady=20)
        
        tk.Label(main_content, text="🚀", font=('Microsoft YaHei UI', 24),
                bg=self.colors['surface']).pack()
        
        main_btn = tk.Button(main_content, text="开始处理", command=self.start_processing,
                            font=('Microsoft YaHei UI', 16, 'bold'),
                            bg=self.colors['accent'], fg='white',
                            relief='flat', bd=0, padx=40, pady=15, cursor='hand2')
        main_btn.pack(pady=(10, 0))
        
        # BOM工具按钮
        bom_card = tk.Frame(button_container, bg=self.colors['surface'],
                           relief='solid', bd=1, highlightbackground=self.colors['divider'])
        bom_card.pack(pady=(10, 0))
        
        bom_content = tk.Frame(bom_card, bg=self.colors['surface'])
        bom_content.pack(padx=20, pady=15)
        
        tk.Label(bom_content, text="📋", font=('Microsoft YaHei UI', 20),
                bg=self.colors['surface']).pack()
        
        bom_btn = tk.Button(bom_content, text="BOM表制作工具", command=self.open_bom_tool,
                           font=('Microsoft YaHei UI', 14, 'bold'),
                           bg=self.colors['secondary'], fg='white',
                           relief='flat', bd=0, padx=30, pady=12, cursor='hand2')
        bom_btn.pack(pady=(8, 0))

    def create_status_bar(self):
        """创建状态栏"""
        status_bar = tk.Frame(self.main_frame, bg=self.colors['surface'],
                             relief='solid', bd=1, highlightbackground=self.colors['divider'])
        status_bar.pack(side='bottom', fill='x')
        
        status_content = tk.Frame(status_bar, bg=self.colors['surface'])
        status_content.pack(fill='x', padx=20, pady=8)
        
        # 左侧时间
        time_frame = tk.Frame(status_content, bg=self.colors['surface'])
        time_frame.pack(side='left')
        
        tk.Label(time_frame, text="🕒", font=('Microsoft YaHei UI', 12),
                bg=self.colors['surface']).pack(side='left', padx=(0, 5))
        
        self.time_label = tk.Label(time_frame, text="",
                                  font=('Microsoft YaHei UI', 10),
                                  fg=self.colors['text_secondary'],
                                  bg=self.colors['surface'])
        self.time_label.pack(side='left')
        
        # 右侧版权
        copyright_frame = tk.Frame(status_content, bg=self.colors['surface'])
        copyright_frame.pack(side='right')
        
        tk.Label(copyright_frame, text="©", font=('Microsoft YaHei UI', 12, 'bold'),
                bg=self.colors['surface'], fg=self.colors['primary']).pack(side='left', padx=(0, 5))
        
        tk.Label(copyright_frame, text="版权所有 © 2025 天锻重工",
                font=('Microsoft YaHei UI', 10),
                fg=self.colors['text_secondary'],
                bg=self.colors['surface']).pack(side='left')

    # 文件选择方法
    def select_data_storage(self):
        file_path = filedialog.askopenfilename(
            title="选择数据储存文件",
            filetypes=[("Excel files", "*.xlsx *.xlsm *.xls")]
        )
        if file_path:
            self.file_vars['data_storage'].set(f"✅ 已选择: {os.path.basename(file_path)}")

    def select_tank_details(self):
        file_paths = filedialog.askopenfilenames(
            title="选择油箱明细表",
            filetypes=[("Excel files", "*.xlsx *.xlsm *.xls")]
        )
        if file_paths:
            self.file_vars['tank_details'].set(f"✅ 已选择: {len(file_paths)} 个文件")

    def select_standard_parts(self):
        file_paths = filedialog.askopenfilenames(
            title="选择标准件表",
            filetypes=[("Excel files", "*.xlsx *.xlsm *.xls")]
        )
        if file_paths:
            self.file_vars['standard_parts'].set(f"✅ 已选择: {len(file_paths)} 个文件")

    def select_bom_files(self):
        file_paths = filedialog.askopenfilenames(
            title="选择BOM表",
            filetypes=[("Excel files", "*.xlsx *.xlsm *.xls")]
        )
        if file_paths:
            self.file_vars['bom_files'].set(f"✅ 已选择: {len(file_paths)} 个文件")

    def select_dwg_folder(self):
        folder_path = filedialog.askdirectory(title="选择DWG文件夹")
        if folder_path:
            self.file_vars['dwg_folder'].set(f"✅ 已选择: {os.path.basename(folder_path)}")

    def select_dxf_folder(self):
        folder_path = filedialog.askdirectory(title="选择DXF输出文件夹")
        if folder_path:
            self.file_vars['dxf_folder'].set(f"✅ 已选择: {os.path.basename(folder_path)}")

    def start_processing(self):
        """开始处理"""
        messagebox.showinfo("处理开始", "数据处理功能正在开发中...")

    def open_bom_tool(self):
        """打开BOM工具"""
        messagebox.showinfo("BOM工具", "BOM表制作工具正在开发中...")

    def update_time(self):
        """更新时间显示"""
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=f"当前时间：{current_time}")
        self.master.after(1000, self.update_time)


def main():
    """主函数"""
    try:
        root = tk.Tk()
        app = ModernEquipmentDataSystem(root)
        root.mainloop()
    except Exception as e:
        print(f"程序启动错误: {str(e)}")
        messagebox.showerror("错误", f"程序启动失败: {str(e)}")


if __name__ == "__main__":
    main()
